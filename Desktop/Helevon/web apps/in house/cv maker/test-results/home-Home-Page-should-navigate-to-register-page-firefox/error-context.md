# Test info

- Name: Home Page >> should navigate to register page
- Location: /home/<USER>/Desktop/Helevon/cv maker/cv-application-maker/tests/e2e/home.spec.ts:41:7

# Error details

```
Error: locator.click: Test timeout of 30000ms exceeded.
Call log:
  - waiting for getByR<PERSON>('link', { name: /register/i })
    - locator resolved to <a href="/register" class="flex items-center text-sm font-medium transition-colors hover:text-foreground/80 relative text-foreground/60">nav.register</a>
  - attempting click action
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <div data-state="open" aria-hidden="true" data-aria-hidden="true" class="fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"></div> intercepts pointer events
    - retrying click action
    - waiting 20ms
    2 × waiting for element to be visible, enabled and stable
      - element is visible, enabled and stable
      - scrolling into view if needed
      - done scrolling
      - <div data-state="open" aria-hidden="true" data-aria-hidden="true" class="fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"></div> intercepts pointer events
    - retrying click action
      - waiting 100ms
    43 × waiting for element to be visible, enabled and stable
       - element is visible, enabled and stable
       - scrolling into view if needed
       - done scrolling
       - <div data-state="open" aria-hidden="true" data-aria-hidden="true" class="fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"></div> intercepts pointer events
     - retrying click action
       - waiting 500ms

    at /home/<USER>/Desktop/Helevon/cv maker/cv-application-maker/tests/e2e/home.spec.ts:45:57
```

# Page snapshot

```yaml
- dialog "Cookie Policy & Terms of Use":
  - heading "Cookie Policy & Terms of Use" [level=2]
  - paragraph: Before using our CV Maker application, please review and accept our Terms of Use and Privacy Policy.
  - paragraph:
    - text: By clicking "Accept", you agree to our
    - link "Terms of Use":
      - /url: /terms
    - text: and
    - link "Privacy Policy":
      - /url: /privacy
    - text: .
  - paragraph: We use cookies to enhance your experience and provide essential functionality. We do not use cookies for tracking or advertising purposes.
  - button "View Terms"
  - button "View Privacy Policy"
  - button "Accept"
  - button "Close"
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Home Page', () => {
   4 |   test('should display the home page correctly', async ({ page }) => {
   5 |     await page.goto('/');
   6 |     
   7 |     // Check page title
   8 |     await expect(page).toHaveTitle(/CV Maker/);
   9 |     
  10 |     // Check header
  11 |     const header = page.locator('header');
  12 |     await expect(header).toBeVisible();
  13 |     await expect(header.getByText('CV Maker by Helevon Technology')).toBeVisible();
  14 |     
  15 |     // Check navigation links
  16 |     await expect(page.getByRole('link', { name: /home/<USER>
  17 |     await expect(page.getByRole('link', { name: /login/i })).toBeVisible();
  18 |     await expect(page.getByRole('link', { name: /register/i })).toBeVisible();
  19 |     
  20 |     // Check footer
  21 |     const footer = page.locator('footer');
  22 |     await expect(footer).toBeVisible();
  23 |   });
  24 |   
  25 |   test('should navigate to login page', async ({ page }) => {
  26 |     await page.goto('/');
  27 |     
  28 |     // Click login link
  29 |     await page.getByRole('link', { name: /login/i }).click();
  30 |     
  31 |     // Check URL
  32 |     await expect(page).toHaveURL(/.*login/);
  33 |     
  34 |     // Check login form
  35 |     await expect(page.getByRole('heading', { name: /login/i })).toBeVisible();
  36 |     await expect(page.getByLabel(/email/i)).toBeVisible();
  37 |     await expect(page.getByLabel(/password/i)).toBeVisible();
  38 |     await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();
  39 |   });
  40 |   
  41 |   test('should navigate to register page', async ({ page }) => {
  42 |     await page.goto('/');
  43 |     
  44 |     // Click register link
> 45 |     await page.getByRole('link', { name: /register/i }).click();
     |                                                         ^ Error: locator.click: Test timeout of 30000ms exceeded.
  46 |     
  47 |     // Check URL
  48 |     await expect(page).toHaveURL(/.*register/);
  49 |     
  50 |     // Check registration form
  51 |     await expect(page.getByRole('heading', { name: /register/i })).toBeVisible();
  52 |     await expect(page.getByLabel(/name/i)).toBeVisible();
  53 |     await expect(page.getByLabel(/email/i)).toBeVisible();
  54 |     await expect(page.getByLabel(/password/i)).toBeVisible();
  55 |     await expect(page.getByRole('button', { name: /sign up/i })).toBeVisible();
  56 |   });
  57 | });
  58 |
```