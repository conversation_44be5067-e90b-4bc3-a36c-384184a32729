// scripts/setup-env.js
const fs = require('fs');
const path = require('path');

// This script ensures that the DATABASE_URL is properly set for Netlify
// It creates a .env file with the correct DATABASE_URL if it doesn't exist

// The properly escaped DATABASE_URL with protocol prefix
const DATABASE_URL = "postgresql://helevon-cv-generator-lucifer-as-admin:tuV1%3D%5D%7DQJw%3Ed%2AQ%2Bz@*************:5438/helevon-cv-generator-db";

// Ensure DATABASE_URL starts with postgresql:// protocol
function ensureProtocol(url) {
  if (!url) return DATABASE_URL;
  if (!url.startsWith('postgresql://') && !url.startsWith('postgres://')) {
    return `postgresql://${url.replace(/^["']|["']$/g, '')}`;
  }
  return url;
}

// Check if .env file exists
const envPath = path.join(process.cwd(), '.env');
if (!fs.existsSync(envPath)) {
  // Create .env file with DATABASE_URL
  fs.writeFileSync(envPath, `DATABASE_URL="${DATABASE_URL}"\n`);
  console.log('Created .env file with DATABASE_URL');
} else {
  // Read .env file
  const envContent = fs.readFileSync(envPath, 'utf8');

  // Check if DATABASE_URL is already set
  if (!envContent.includes('DATABASE_URL=')) {
    // Append DATABASE_URL to .env file
    fs.appendFileSync(envPath, `\nDATABASE_URL="${DATABASE_URL}"\n`);
    console.log('Added DATABASE_URL to .env file');
  } else {
    // Extract current DATABASE_URL
    const match = envContent.match(/DATABASE_URL=["']?(.*?)["']?(\r?\n|$)/);
    const currentUrl = match ? match[1] : '';
    const correctedUrl = ensureProtocol(currentUrl);

    // Replace DATABASE_URL in .env file
    const newEnvContent = envContent.replace(
      /DATABASE_URL=.*(\r?\n|$)/,
      `DATABASE_URL="${correctedUrl}"\n`
    );
    fs.writeFileSync(envPath, newEnvContent);
    console.log('Updated DATABASE_URL in .env file');
  }
}

// Also create a .env.production file for Netlify
const envProdPath = path.join(process.cwd(), '.env.production');
fs.writeFileSync(envProdPath, `DATABASE_URL="${DATABASE_URL}"\n`);
console.log('Created/updated .env.production file');

console.log('Environment setup complete');
