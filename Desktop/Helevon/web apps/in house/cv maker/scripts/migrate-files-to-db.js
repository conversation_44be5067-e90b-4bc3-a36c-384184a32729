// scripts/migrate-files-to-db.js
// This script migrates existing files from the filesystem to the database

const { PrismaClient } = require('@prisma/client');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const readFile = promisify(fs.readFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

const prisma = new PrismaClient();

async function migrateFilesToDatabase() {
  try {
    console.log('Starting file migration to database...');
    
    // Get all files from the database
    const files = await prisma.file.findMany();
    console.log(`Found ${files.length} files in the database`);
    
    // Base uploads directory
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads');
    
    // Check if uploads directory exists
    if (!fs.existsSync(uploadsDir)) {
      console.log('Uploads directory does not exist, nothing to migrate');
      return;
    }
    
    // Get all user directories
    const userDirs = await readdir(uploadsDir);
    console.log(`Found ${userDirs.length} user directories`);
    
    let migratedCount = 0;
    let errorCount = 0;
    
    // Process each user directory
    for (const userId of userDirs) {
      const userDir = path.join(uploadsDir, userId);
      const isDirectory = (await stat(userDir)).isDirectory();
      
      if (!isDirectory) continue;
      
      // Get all files in the user directory
      const userFiles = await readdir(userDir);
      console.log(`Found ${userFiles.length} files for user ${userId}`);
      
      // Process each file
      for (const fileName of userFiles) {
        const filePath = path.join(userDir, fileName);
        
        try {
          // Find the corresponding file record in the database
          const fileUrl = `/uploads/${userId}/${fileName}`;
          const fileRecord = files.find(f => f.url === fileUrl);
          
          if (!fileRecord) {
            console.log(`No database record found for file: ${filePath}`);
            continue;
          }
          
          // Check if the file already has data
          if (fileRecord.fileData) {
            console.log(`File ${fileRecord.id} already has data, skipping`);
            continue;
          }
          
          // Read the file
          const fileBuffer = await readFile(filePath);
          const fileData = fileBuffer.toString('base64');
          
          // Update the file record with the file data
          await prisma.file.update({
            where: { id: fileRecord.id },
            data: { fileData },
          });
          
          console.log(`Migrated file: ${fileRecord.id} (${fileRecord.name})`);
          migratedCount++;
        } catch (error) {
          console.error(`Error migrating file ${filePath}:`, error);
          errorCount++;
        }
      }
    }
    
    console.log(`Migration complete. Migrated ${migratedCount} files with ${errorCount} errors.`);
  } catch (error) {
    console.error('Migration error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the migration
migrateFilesToDatabase();
