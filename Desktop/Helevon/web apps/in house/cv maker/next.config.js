/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Don't bundle bcrypt on the client side
      config.resolve.fallback = {
        ...config.resolve.fallback,
        bcrypt: false,
        fs: false,
        net: false,
        tls: false,
        child_process: false,
      };
    }
    return config;
  },
  // Disable ESLint during build
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable TypeScript type checking during build
  typescript: {
    ignoreBuildErrors: true,
  },
  // Configure image domains
  images: {
    domains: ['cv.helevon.org', 'localhost'],
    unoptimized: true,
  },
  // Configure trailing slash for better compatibility
  trailingSlash: true,
  // Configure asset prefix for better CDN compatibility
  assetPrefix: process.env.NODE_ENV === 'production' ? 'https://cv.helevon.org' : '',
  env: {
    CAN_EXPORT: process.env.CAN_EXPORT || 'true',
  },
};

module.exports = nextConfig;
