# Netlify headers file

# Set proper content types for Next.js static assets
/_next/static/chunks/*.js
  Content-Type: application/javascript
  Cache-Control: public, max-age=31536000, immutable

/_next/static/css/*.css
  Content-Type: text/css
  Cache-Control: public, max-age=31536000, immutable

/_next/static/media/*.woff2
  Content-Type: font/woff2
  Cache-Control: public, max-age=31536000, immutable

/_next/static/media/*.woff
  Content-Type: font/woff
  Cache-Control: public, max-age=31536000, immutable

/_next/static/media/*.ttf
  Content-Type: font/ttf
  Cache-Control: public, max-age=31536000, immutable

/_next/static/media/*.eot
  Content-Type: application/vnd.ms-fontobject
  Cache-Control: public, max-age=31536000, immutable

/_next/static/media/*.svg
  Content-Type: image/svg+xml
  Cache-Control: public, max-age=31536000, immutable

/_next/static/media/*.png
  Content-Type: image/png
  Cache-Control: public, max-age=31536000, immutable

/_next/static/media/*.jpg
  Content-Type: image/jpeg
  Cache-Control: public, max-age=31536000, immutable

/_next/static/media/*.gif
  Content-Type: image/gif
  Cache-Control: public, max-age=31536000, immutable

# Security headers for all other routes
/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), interest-cohort=()
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self'; frame-src 'self'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; object-src 'none'; upgrade-insecure-requests;
