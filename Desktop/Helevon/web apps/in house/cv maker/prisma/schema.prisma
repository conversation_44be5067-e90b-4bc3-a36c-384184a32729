// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// NextAuth.js Models
model Account {
  id                String  @id @default(uuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
}

model Session {
  id           String   @id @default(uuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id            String        @id @default(uuid())
  name          String?
  email         String?       @unique
  emailVerified DateTime?
  password      String?
  image         String?
  language      String        @default("en") // Default language preference
  accounts      Account[]
  sessions      Session[]
  cvs           CV[]
  files         File[]
  certificates  Certificate[]
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Security and rate limiting
  lastLoginAt   DateTime?
  loginAttempts Int           @default(0)
  lockedUntil   DateTime?
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

// CV Application Models
model CV {
  id             String   @id @default(uuid())
  userId         String
  title          String
  template       String   @default("standard") // Template style
  language       String   @default("de") // CV language (not UI language)
  personalInfo   Json?    // Stores personal information as JSON
  education      Json?    // Stores education entries as JSON
  workExperience Json?    // Stores work experience entries as JSON
  skills         Json?    // Stores skills as JSON
  references     Json?    // Stores references as JSON
  coverLetter    String? // Rich text content for cover letter
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  user  User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  files File[]
}

model File {
  id        String   @id @default(uuid())
  userId    String
  cvId      String?
  name      String
  type      String   // MIME type
  size      Int
  url       String
  fileData  String?  @db.Text  // Base64 encoded file data
  category  String   // photo, certificate, cover_letter, etc.
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  cv   CV?  @relation(fields: [cvId], references: [id], onDelete: SetNull)
}

model Certificate {
  id              Int       @id @default(autoincrement())
  userId          String
  educationEntryId Int?
  fileName        String
  fileType        String
  fileSize        Int
  fileData        String    @db.Text  // Base64 encoded file data
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  isDeleted       Boolean   @default(false)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}
