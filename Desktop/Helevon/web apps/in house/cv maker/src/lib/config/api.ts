/**
 * API Configuration
 * 
 * Centralized configuration for API endpoints and backend communication.
 * Supports configurable backend URL for microservices architecture.
 */

// Get the backend base URL from environment variables or default to current origin
const getBackendBaseUrl = (): string => {
  // Check for environment variable first
  if (typeof window === 'undefined') {
    // Server-side: use environment variable or default
    return process.env.BACKEND_BASE_URL || process.env.NEXTAUTH_URL || 'http://localhost:3000';
  } else {
    // Client-side: use environment variable or current origin
    return process.env.NEXT_PUBLIC_BACKEND_BASE_URL || window.location.origin;
  }
};

export const API_CONFIG = {
  BASE_URL: getBackendBaseUrl(),
  ENDPOINTS: {
    // Authentication endpoints
    AUTH: {
      SIGNIN: '/api/auth/signin',
      SIGNOUT: '/api/auth/signout',
      SESSION: '/api/auth/session',
      VERIFY_PASSWORD: '/api/auth/verify-password',
    },
    
    // User management endpoints
    USER: {
      ACCOUNT: '/api/user/account',
    },
    
    // CV management endpoints
    CV: {
      BASE: '/api/cv',
      BY_ID: (id: string) => `/api/cv/${id}`,
      PERSONAL_INFO: (id: string) => `/api/cv/${id}/personal-info`,
      EDUCATION: (id: string) => `/api/cv/${id}/education`,
      WORK_EXPERIENCE: (id: string) => `/api/cv/${id}/work-experience`,
      SKILLS: (id: string) => `/api/cv/${id}/skills`,
      REFERENCES: (id: string) => `/api/cv/${id}/references`,
      COVER_LETTER: (id: string) => `/api/cv/${id}/cover-letter`,
      UPLOAD: (id: string) => `/api/cv/${id}/upload`,
      FILE: (id: string, fileId: string) => `/api/cv/${id}/file/${fileId}`,
      EXPORT: (id: string) => `/api/cv/${id}/export`,
    },
    
    // Metrics endpoint
    METRICS: '/api/metrics',
  },
} as const;

/**
 * Build full URL for an endpoint
 */
export const buildApiUrl = (endpoint: string): string => {
  const baseUrl = API_CONFIG.BASE_URL.replace(/\/$/, ''); // Remove trailing slash
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
  return `${baseUrl}${cleanEndpoint}`;
};

/**
 * Default fetch options with common headers
 */
export const getDefaultFetchOptions = (): RequestInit => ({
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include', // Include cookies for authentication
});

/**
 * Create fetch options for file uploads
 */
export const getFileUploadOptions = (): Omit<RequestInit, 'body'> => ({
  credentials: 'include', // Include cookies for authentication
  // Don't set Content-Type for FormData - browser will set it with boundary
});

/**
 * API client configuration
 */
export const API_CLIENT_CONFIG = {
  timeout: 30000, // 30 seconds
  retries: 3,
  retryDelay: 1000, // 1 second
} as const;

/**
 * Check if the API is configured for external backend
 */
export const isExternalBackend = (): boolean => {
  const backendUrl = API_CONFIG.BASE_URL;
  if (typeof window === 'undefined') {
    return false; // Server-side always uses local
  }
  return backendUrl !== window.location.origin;
};

/**
 * Get API health check URL
 */
export const getHealthCheckUrl = (): string => {
  return buildApiUrl(API_CONFIG.ENDPOINTS.METRICS);
};
