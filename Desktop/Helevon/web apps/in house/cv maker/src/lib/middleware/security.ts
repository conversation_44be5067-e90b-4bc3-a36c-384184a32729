import { NextRequest, NextResponse } from 'next/server';

/**
 * Security middleware to add security headers
 */
export function securityMiddleware(req: NextRequest) {
  const response = NextResponse.next();

  // Add security headers
  const headers = response.headers;

  // Content Security Policy - More restrictive and specific
  headers.set(
    'Content-Security-Policy',
    "default-src 'self'; " +
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'; " +
    "style-src 'self' 'unsafe-inline'; " +
    "img-src 'self' data: blob: https://helevon.com; " +
    "font-src 'self' data:; " +
    "connect-src 'self' https://helevon.com; " +
    "frame-src 'self'; " +
    "base-uri 'self'; " +
    "form-action 'self'; " +
    "frame-ancestors 'none'; " +
    "object-src 'none'; " +
    "upgrade-insecure-requests;"
  );

  // XSS Protection
  headers.set('X-XSS-Protection', '1; mode=block');

  // Only set X-Content-Type-Options for non-static assets
  if (!req.nextUrl.pathname.startsWith('/_next/static/')) {
    headers.set('X-Content-Type-Options', 'nosniff');
  }

  // Referrer Policy
  headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

  // Frame Options
  headers.set('X-Frame-Options', 'DENY');

  // Permissions Policy
  headers.set(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(), interest-cohort=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()'
  );

  // Strict Transport Security
  if (process.env.NODE_ENV === 'production') {
    headers.set(
      'Strict-Transport-Security',
      'max-age=63072000; includeSubDomains; preload'
    );
  }

  // Cache Control - Prevent caching of sensitive pages
  if (
    req.nextUrl.pathname.startsWith('/dashboard') ||
    req.nextUrl.pathname.startsWith('/cv') ||
    req.nextUrl.pathname.startsWith('/api')
  ) {
    headers.set(
      'Cache-Control',
      'no-store, no-cache, must-revalidate, proxy-revalidate'
    );
    headers.set('Pragma', 'no-cache');
    headers.set('Expires', '0');
  }

  return response;
}
