'use client';

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useCVStore, CVWithFiles } from '@/lib/stores/cv-store';

interface CVContextType {
  cv: CVWithFiles;
  refreshCV: () => Promise<void>;
  isRefreshing: boolean;
}

const CVContext = createContext<CVContextType | undefined>(undefined);

export const useCV = () => {
  const context = useContext(CVContext);
  if (!context) {
    throw new Error('useCV must be used within a CVProvider');
  }
  return context;
};

interface CVProviderProps {
  children: ReactNode;
  initialCV: CVWithFiles;
}

export function CVProvider({ children, initialCV }: CVProviderProps) {
  const {
    currentCV,
    fetchCV,
    setCurrentCV,
    isLoading
  } = useCVStore();

  // Initialize the current CV when the provider mounts
  useEffect(() => {
    if (!currentCV || currentCV.id !== initialCV.id) {
      setCurrentCV(initialCV);
    }
  }, [initialCV, currentCV, setCurrentCV]);

  // Function to refresh CV data from the server
  const refreshCV = async () => {
    await fetchCV(initialCV.id);
  };

  // Use current CV from store or fallback to initial CV
  const cv = currentCV && currentCV.id === initialCV.id ? currentCV : initialCV;

  return (
    <CVContext.Provider value={{
      cv,
      refreshCV,
      isRefreshing: isLoading
    }}>
      {children}
    </CVContext.Provider>
  );
}
