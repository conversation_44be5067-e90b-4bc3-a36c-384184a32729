'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { CV } from '@prisma/client';
import { EducationEntry, WorkExperienceEntry, Skill, Reference, PersonalInfo } from '@/types/cv';

interface CVContextType {
  cv: CV & {
    files: {
      id: string;
      name: string;
      url: string;
      category: string;
    }[];
  };
  refreshCV: () => Promise<void>;
  isRefreshing: boolean;
}

const CVContext = createContext<CVContextType | undefined>(undefined);

export const useCV = () => {
  const context = useContext(CVContext);
  if (!context) {
    throw new Error('useCV must be used within a CVProvider');
  }
  return context;
};

interface CVProviderProps {
  children: ReactNode;
  initialCV: CV & {
    files: {
      id: string;
      name: string;
      url: string;
      category: string;
    }[];
  };
}

export function CVProvider({ children, initialCV }: CVProviderProps) {
  const [cv, setCV] = useState<typeof initialCV>(initialCV);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Function to refresh CV data from the server
  const refreshCV = async () => {
    setIsRefreshing(true);
    try {
      const response = await fetch(`/api/cv/${initialCV.id}`);
      if (!response.ok) {
        throw new Error('Failed to refresh CV data');
      }
      const updatedCV = await response.json();
      setCV(updatedCV);
    } catch (error) {
      console.error('Error refreshing CV:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <CVContext.Provider value={{ cv, refreshCV, isRefreshing }}>
      {children}
    </CVContext.Provider>
  );
}
