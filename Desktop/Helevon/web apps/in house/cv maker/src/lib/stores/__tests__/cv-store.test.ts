/**
 * CV Store Tests
 * 
 * Tests for the CV Zustand store functionality.
 */

import { renderHook, act } from '@testing-library/react';
import { useCVStore } from '../cv-store';
import { api } from '../../api/client';

// Mock the API client
jest.mock('../../api/client');
const mockApi = api as jest.Mocked<typeof api>;

describe('CV Store', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the store state
    useCVStore.getState().reset();
  });

  describe('Initial state', () => {
    it('should have correct initial state', () => {
      const { result } = renderHook(() => useCVStore());
      
      expect(result.current.cvs).toEqual([]);
      expect(result.current.currentCV).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });
  });

  describe('fetchCVs', () => {
    it('should fetch CVs successfully', async () => {
      const mockCVs = [
        {
          id: '1',
          title: 'Test CV',
          template: 'standard',
          language: 'en',
          files: [],
        },
      ];

      mockApi.get.mockResolvedValueOnce(mockCVs);

      const { result } = renderHook(() => useCVStore());

      await act(async () => {
        await result.current.fetchCVs();
      });

      expect(mockApi.get).toHaveBeenCalledWith('/api/cv');
      expect(result.current.cvs).toEqual(mockCVs);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBeNull();
    });

    it('should handle fetch error', async () => {
      const errorMessage = 'Failed to fetch CVs';
      mockApi.get.mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useCVStore());

      await act(async () => {
        await result.current.fetchCVs();
      });

      expect(result.current.cvs).toEqual([]);
      expect(result.current.isLoading).toBe(false);
      expect(result.current.error).toBe(errorMessage);
    });
  });

  describe('createCV', () => {
    it('should create CV successfully', async () => {
      const newCVData = {
        title: 'New CV',
        template: 'modern' as const,
        language: 'en',
        userId: 'user-1',
      };

      const mockCreatedCV = {
        id: '1',
        ...newCVData,
        files: [],
        personalInfo: {},
        education: [],
        workExperience: [],
        skills: [],
        references: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockApi.post.mockResolvedValueOnce(mockCreatedCV);

      const { result } = renderHook(() => useCVStore());

      let createdCV;
      await act(async () => {
        createdCV = await result.current.createCV(newCVData);
      });

      expect(mockApi.post).toHaveBeenCalledWith('/api/cv', newCVData);
      expect(createdCV).toEqual(mockCreatedCV);
      expect(result.current.cvs).toContain(mockCreatedCV);
      expect(result.current.currentCV).toEqual(mockCreatedCV);
    });

    it('should handle create error', async () => {
      const newCVData = {
        title: 'New CV',
        template: 'modern' as const,
        language: 'en',
        userId: 'user-1',
      };

      const errorMessage = 'Failed to create CV';
      mockApi.post.mockRejectedValueOnce(new Error(errorMessage));

      const { result } = renderHook(() => useCVStore());

      await act(async () => {
        try {
          await result.current.createCV(newCVData);
        } catch (error) {
          // Expected to throw
        }
      });

      expect(result.current.error).toBe(errorMessage);
      expect(result.current.cvs).toEqual([]);
      expect(result.current.currentCV).toBeNull();
    });
  });

  describe('updatePersonalInfo', () => {
    it('should update personal info and refresh CV', async () => {
      const cvId = '1';
      const personalInfo = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone: '************',
        address: '123 Main St',
        city: 'Anytown',
        postalCode: '12345',
        country: 'USA',
      };

      const mockUpdatedCV = {
        id: cvId,
        personalInfo,
        files: [],
      };

      mockApi.put.mockResolvedValueOnce({});
      mockApi.get.mockResolvedValueOnce(mockUpdatedCV);

      const { result } = renderHook(() => useCVStore());

      await act(async () => {
        await result.current.updatePersonalInfo(cvId, personalInfo);
      });

      expect(mockApi.put).toHaveBeenCalledWith(`/api/cv/${cvId}/personal-info`, personalInfo);
      expect(mockApi.get).toHaveBeenCalledWith(`/api/cv/${cvId}`);
    });
  });

  describe('uploadFile', () => {
    it('should upload file and refresh CV', async () => {
      const cvId = '1';
      const file = new File(['test'], 'test.txt', { type: 'text/plain' });
      const category = 'certificate';

      const mockUploadedFile = {
        id: 'file-1',
        name: 'test.txt',
        url: '/files/file-1',
        category,
        type: 'text/plain',
        size: 4,
      };

      const mockUpdatedCV = {
        id: cvId,
        files: [mockUploadedFile],
      };

      mockApi.uploadFile.mockResolvedValueOnce(mockUploadedFile);
      mockApi.get.mockResolvedValueOnce(mockUpdatedCV);

      const { result } = renderHook(() => useCVStore());

      let uploadedFile;
      await act(async () => {
        uploadedFile = await result.current.uploadFile(cvId, file, category);
      });

      expect(mockApi.uploadFile).toHaveBeenCalledWith(
        `/api/cv/${cvId}/upload`,
        expect.any(FormData)
      );
      expect(mockApi.get).toHaveBeenCalledWith(`/api/cv/${cvId}`);
      expect(uploadedFile).toEqual(mockUploadedFile);
    });
  });

  describe('Store utilities', () => {
    it('should set current CV', () => {
      const mockCV = {
        id: '1',
        title: 'Test CV',
        files: [],
      };

      const { result } = renderHook(() => useCVStore());

      act(() => {
        result.current.setCurrentCV(mockCV as any);
      });

      expect(result.current.currentCV).toEqual(mockCV);
    });

    it('should clear error', () => {
      const { result } = renderHook(() => useCVStore());

      // Set an error first
      act(() => {
        useCVStore.setState({ error: 'Test error' });
      });

      expect(result.current.error).toBe('Test error');

      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });

    it('should reset store', () => {
      const { result } = renderHook(() => useCVStore());

      // Set some state first
      act(() => {
        useCVStore.setState({
          cvs: [{ id: '1' } as any],
          currentCV: { id: '1' } as any,
          error: 'Test error',
        });
      });

      act(() => {
        result.current.reset();
      });

      expect(result.current.cvs).toEqual([]);
      expect(result.current.currentCV).toBeNull();
      expect(result.current.error).toBeNull();
    });
  });
});
