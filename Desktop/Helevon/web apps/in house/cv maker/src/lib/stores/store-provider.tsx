/**
 * Store Provider
 * 
 * Provider component that initializes and manages Zustand stores,
 * handles store hydration, and provides store context to the app.
 */

'use client';

import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { useCVStore } from './cv-store';
import { useUserStore } from './user-store';
import { useAppStore } from './app-store';

interface StoreContextType {
  isInitialized: boolean;
}

const StoreContext = createContext<StoreContextType>({
  isInitialized: false,
});

export const useStoreContext = () => {
  const context = useContext(StoreContext);
  if (!context) {
    throw new Error('useStoreContext must be used within a StoreProvider');
  }
  return context;
};

interface StoreProviderProps {
  children: ReactNode;
}

export function StoreProvider({ children }: StoreProviderProps) {
  const { data: session, status } = useSession();
  const [isInitialized, setIsInitialized] = React.useState(false);
  
  // Store actions
  const setUser = useUserStore((state) => state.setUser);
  const setAuthenticated = useUserStore((state) => state.setAuthenticated);
  const fetchUser = useUserStore((state) => state.fetchUser);
  const checkHealth = useAppStore((state) => state.checkHealth);
  const resetCVStore = useCVStore((state) => state.reset);
  const resetUserStore = useUserStore((state) => state.reset);

  // Initialize stores when session changes
  useEffect(() => {
    const initializeStores = async () => {
      try {
        // Check backend health
        await checkHealth();

        if (session?.user) {
          // User is authenticated
          setUser({
            id: session.user.id,
            name: session.user.name || undefined,
            email: session.user.email || undefined,
            image: session.user.image || undefined,
            language: 'en', // Default, will be updated by fetchUser
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          });
          setAuthenticated(true);

          // Fetch full user data
          try {
            await fetchUser();
          } catch (error) {
            console.warn('Failed to fetch user data:', error);
          }
        } else {
          // User is not authenticated
          setAuthenticated(false);
          resetCVStore();
          resetUserStore();
        }
      } catch (error) {
        console.error('Failed to initialize stores:', error);
      } finally {
        setIsInitialized(true);
      }
    };

    if (status !== 'loading') {
      initializeStores();
    }
  }, [session, status, setUser, setAuthenticated, fetchUser, checkHealth, resetCVStore, resetUserStore]);

  // Periodic health check
  useEffect(() => {
    const interval = setInterval(() => {
      checkHealth();
    }, 60000); // Check every minute

    return () => clearInterval(interval);
  }, [checkHealth]);

  const contextValue: StoreContextType = {
    isInitialized,
  };

  return (
    <StoreContext.Provider value={contextValue}>
      {children}
    </StoreContext.Provider>
  );
}

/**
 * Hook to get all store states and actions
 */
export const useStores = () => {
  const cvStore = useCVStore();
  const userStore = useUserStore();
  const appStore = useAppStore();

  return {
    cv: cvStore,
    user: userStore,
    app: appStore,
  };
};

/**
 * Hook for CV operations
 */
export const useCV = () => {
  return useCVStore();
};

/**
 * Hook for user operations
 */
export const useUser = () => {
  return useUserStore();
};

/**
 * Hook for app operations
 */
export const useApp = () => {
  return useAppStore();
};
