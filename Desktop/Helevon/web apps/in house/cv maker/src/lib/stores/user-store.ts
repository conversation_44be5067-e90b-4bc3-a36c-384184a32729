/**
 * User Store
 * 
 * Zustand store for managing user state, authentication status,
 * and account management.
 */

import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { api } from '@/lib/api/client';
import { API_CONFIG } from '@/lib/config/api';

export interface User {
  id: string;
  name?: string;
  email?: string;
  image?: string;
  language: string;
  createdAt: string;
  updatedAt: string;
}

export interface UpdateAccountData {
  name: string;
  email: string;
  language: 'en' | 'de' | 'ar';
  currentPassword?: string;
  newPassword?: string;
  confirmPassword?: string;
}

interface UserState {
  // State
  user: User | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
  
  // Actions
  fetchUser: () => Promise<void>;
  updateAccount: (data: UpdateAccountData) => Promise<void>;
  setUser: (user: User | null) => void;
  setAuthenticated: (isAuthenticated: boolean) => void;
  clearError: () => void;
  logout: () => void;
  reset: () => void;
}

const initialState = {
  user: null,
  isLoading: false,
  error: null,
  isAuthenticated: false,
};

export const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set, get) => ({
        ...initialState,

        // Fetch current user account information
        fetchUser: async () => {
          set({ isLoading: true, error: null });
          try {
            const user = await api.get<User>(API_CONFIG.ENDPOINTS.USER.ACCOUNT);
            set({ 
              user, 
              isAuthenticated: true, 
              isLoading: false 
            });
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to fetch user',
              isAuthenticated: false,
              user: null,
              isLoading: false 
            });
          }
        },

        // Update user account information
        updateAccount: async (data: UpdateAccountData) => {
          set({ isLoading: true, error: null });
          try {
            const updatedUser = await api.put<User>(API_CONFIG.ENDPOINTS.USER.ACCOUNT, data);
            set({ 
              user: updatedUser, 
              isLoading: false 
            });
          } catch (error) {
            set({ 
              error: error instanceof Error ? error.message : 'Failed to update account',
              isLoading: false 
            });
            throw error;
          }
        },

        // Set user (typically called by authentication provider)
        setUser: (user: User | null) => {
          set({ 
            user, 
            isAuthenticated: !!user,
            error: null 
          });
        },

        // Set authentication status
        setAuthenticated: (isAuthenticated: boolean) => {
          set({ 
            isAuthenticated,
            user: isAuthenticated ? get().user : null 
          });
        },

        // Clear error
        clearError: () => {
          set({ error: null });
        },

        // Logout user
        logout: () => {
          set({
            user: null,
            isAuthenticated: false,
            error: null,
          });
        },

        // Reset store
        reset: () => {
          set(initialState);
        },
      }),
      {
        name: 'user-store',
        partialize: (state) => ({
          user: state.user,
          isAuthenticated: state.isAuthenticated,
        }),
      }
    ),
    {
      name: 'user-store',
    }
  )
);
