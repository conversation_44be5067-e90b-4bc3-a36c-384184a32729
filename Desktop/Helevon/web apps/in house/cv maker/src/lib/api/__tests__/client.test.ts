/**
 * API Client Tests
 * 
 * Tests for the API client functionality, configuration, and error handling.
 */

import { ApiClient, ApiError } from '../client';
import { API_CONFIG, buildApiUrl } from '../../config/api';

// Mock fetch globally
global.fetch = jest.fn();

describe('API Client', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockClear();
  });

  describe('buildApiUrl', () => {
    it('should build correct URLs', () => {
      const endpoint = '/api/test';
      const url = buildApiUrl(endpoint);
      expect(url).toMatch(/\/api\/test$/);
    });

    it('should handle endpoints without leading slash', () => {
      const endpoint = 'api/test';
      const url = buildApiUrl(endpoint);
      expect(url).toMatch(/\/api\/test$/);
    });

    it('should remove trailing slash from base URL', () => {
      const endpoint = '/api/test';
      const url = buildApiUrl(endpoint);
      expect(url).not.toMatch(/\/\/api\/test$/);
    });
  });

  describe('GET requests', () => {
    it('should make successful GET request', async () => {
      const mockResponse = { data: 'test' };
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Map([['content-type', 'application/json']]),
      });

      const result = await ApiClient.get('/api/test');
      
      expect(fetch).toHaveBeenCalledWith(
        expect.stringMatching(/\/api\/test$/),
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          credentials: 'include',
        })
      );
      expect(result).toEqual(mockResponse);
    });

    it('should handle 404 errors', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
        json: async () => ({ message: 'Resource not found' }),
      });

      await expect(ApiClient.get('/api/nonexistent')).rejects.toThrow(ApiError);
    });
  });

  describe('POST requests', () => {
    it('should make successful POST request with data', async () => {
      const mockData = { name: 'test' };
      const mockResponse = { id: '1', ...mockData };
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Map([['content-type', 'application/json']]),
      });

      const result = await ApiClient.post('/api/test', mockData);
      
      expect(fetch).toHaveBeenCalledWith(
        expect.stringMatching(/\/api\/test$/),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: JSON.stringify(mockData),
          credentials: 'include',
        })
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('File upload', () => {
    it('should upload files correctly', async () => {
      const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });
      const formData = new FormData();
      formData.append('file', mockFile);
      
      const mockResponse = { id: '1', url: '/files/1' };
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Map([['content-type', 'application/json']]),
      });

      const result = await ApiClient.uploadFile('/api/upload', formData);
      
      expect(fetch).toHaveBeenCalledWith(
        expect.stringMatching(/\/api\/upload$/),
        expect.objectContaining({
          method: 'POST',
          body: formData,
          credentials: 'include',
        })
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error handling', () => {
    it('should handle network errors', async () => {
      (fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'));

      await expect(ApiClient.get('/api/test')).rejects.toThrow('Network error');
    });

    it('should handle invalid JSON responses', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => {
          throw new Error('Invalid JSON');
        },
        headers: new Map([['content-type', 'application/json']]),
      });

      await expect(ApiClient.get('/api/test')).rejects.toThrow(ApiError);
    });

    it('should handle empty responses', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        status: 204,
        headers: new Map([['content-length', '0']]),
      });

      const result = await ApiClient.get('/api/test');
      expect(result).toEqual({});
    });
  });

  describe('Health check', () => {
    it('should perform health check', async () => {
      const mockResponse = {
        status: 'ok',
        message: 'Application is running',
        timestamp: new Date().toISOString(),
      };
      
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
        headers: new Map([['content-type', 'application/json']]),
      });

      const result = await ApiClient.healthCheck();
      
      expect(fetch).toHaveBeenCalledWith(
        expect.stringMatching(/\/api\/metrics$/),
        expect.objectContaining({
          method: 'GET',
        })
      );
      expect(result).toEqual(mockResponse);
    });
  });
});

describe('API Configuration', () => {
  it('should have correct endpoint definitions', () => {
    expect(API_CONFIG.ENDPOINTS.CV.BASE).toBe('/api/cv');
    expect(API_CONFIG.ENDPOINTS.CV.BY_ID('123')).toBe('/api/cv/123');
    expect(API_CONFIG.ENDPOINTS.CV.UPLOAD('123')).toBe('/api/cv/123/upload');
    expect(API_CONFIG.ENDPOINTS.USER.ACCOUNT).toBe('/api/user/account');
  });

  it('should build correct file endpoints', () => {
    const cvId = '123';
    const fileId = '456';
    expect(API_CONFIG.ENDPOINTS.CV.FILE(cvId, fileId)).toBe('/api/cv/123/file/456');
  });
});
