/**
 * API Client
 * 
 * Centralized API client with configurable backend URL, error handling,
 * retry logic, and type-safe request/response handling.
 */

import { buildApiUrl, getDefaultFetchOptions, getFileUploadOptions, API_CLIENT_CONFIG } from '@/lib/config/api';

export class ApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public response?: Response
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

/**
 * Sleep utility for retry delays
 */
const sleep = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

/**
 * Enhanced fetch with retry logic and error handling
 */
async function fetchWithRetry(
  url: string,
  options: RequestInit,
  retries = API_CLIENT_CONFIG.retries
): Promise<Response> {
  let lastError: Error;

  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), API_CLIENT_CONFIG.timeout);

      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      // If successful or client error (4xx), don't retry
      if (response.ok || (response.status >= 400 && response.status < 500)) {
        return response;
      }

      // Server error (5xx) - retry
      throw new ApiError(
        `Server error: ${response.status} ${response.statusText}`,
        response.status,
        response
      );
    } catch (error) {
      lastError = error instanceof Error ? error : new Error('Unknown error');
      
      // Don't retry on client errors or last attempt
      if (error instanceof ApiError && error.status < 500) {
        throw error;
      }
      
      if (attempt === retries) {
        throw lastError;
      }

      // Wait before retrying
      await sleep(API_CLIENT_CONFIG.retryDelay * (attempt + 1));
    }
  }

  throw lastError!;
}

/**
 * Process API response and handle errors
 */
async function processResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
    
    try {
      const errorData = await response.json();
      errorMessage = errorData.message || errorMessage;
    } catch {
      // If we can't parse JSON, use the default error message
    }
    
    throw new ApiError(errorMessage, response.status, response);
  }

  // Handle empty responses
  if (response.status === 204 || response.headers.get('content-length') === '0') {
    return {} as T;
  }

  // Handle non-JSON responses (like PDF downloads)
  const contentType = response.headers.get('content-type');
  if (contentType && !contentType.includes('application/json')) {
    return response as unknown as T;
  }

  try {
    return await response.json();
  } catch (error) {
    throw new ApiError('Invalid JSON response', response.status, response);
  }
}

/**
 * API Client class with typed methods
 */
export class ApiClient {
  /**
   * GET request
   */
  static async get<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getDefaultFetchOptions(),
      ...options,
      method: 'GET',
    };

    const response = await fetchWithRetry(url, fetchOptions);
    return processResponse<T>(response);
  }

  /**
   * POST request
   */
  static async post<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getDefaultFetchOptions(),
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    };

    const response = await fetchWithRetry(url, fetchOptions);
    return processResponse<T>(response);
  }

  /**
   * PUT request
   */
  static async put<T>(endpoint: string, data?: any, options?: RequestInit): Promise<T> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getDefaultFetchOptions(),
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    };

    const response = await fetchWithRetry(url, fetchOptions);
    return processResponse<T>(response);
  }

  /**
   * DELETE request
   */
  static async delete<T>(endpoint: string, options?: RequestInit): Promise<T> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getDefaultFetchOptions(),
      ...options,
      method: 'DELETE',
    };

    const response = await fetchWithRetry(url, fetchOptions);
    return processResponse<T>(response);
  }

  /**
   * File upload request
   */
  static async uploadFile<T>(endpoint: string, formData: FormData, options?: RequestInit): Promise<T> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getFileUploadOptions(),
      ...options,
      method: 'POST',
      body: formData,
    };

    const response = await fetchWithRetry(url, fetchOptions);
    return processResponse<T>(response);
  }

  /**
   * Download file request
   */
  static async downloadFile(endpoint: string, options?: RequestInit): Promise<Response> {
    const url = buildApiUrl(endpoint);
    const fetchOptions = {
      ...getDefaultFetchOptions(),
      ...options,
      method: 'GET',
    };

    return fetchWithRetry(url, fetchOptions);
  }

  /**
   * Health check
   */
  static async healthCheck(): Promise<{ status: string; message: string; timestamp: string }> {
    return this.get('/api/metrics');
  }
}

/**
 * Convenience methods for common API patterns
 */
export const api = {
  get: ApiClient.get,
  post: ApiClient.post,
  put: ApiClient.put,
  delete: ApiClient.delete,
  uploadFile: ApiClient.uploadFile,
  downloadFile: ApiClient.downloadFile,
  healthCheck: ApiClient.healthCheck,
};

export default ApiClient;
