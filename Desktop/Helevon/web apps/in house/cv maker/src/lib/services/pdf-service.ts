/**
 * PDF Generation Service
 *
 * This service handles PDF generation for CVs.
 */

import { CV } from '@prisma/client';
import { isPdfExportEnabled } from '@/lib/utils/pdf-export';
import { PersonalInfo, EducationEntry, WorkExperienceEntry, Skill, Reference, CoverLetter } from '@/types/cv';
import { PDFDocument, StandardFonts } from 'pdf-lib';
import { mergePdfs, extractCertificatesWithEntries } from '@/lib/utils/pdf-merger';

/**
 * Generate a PDF for a CV
 *
 * @param cv The CV to generate a PDF for
 * @param files The files associated with the CV
 * @returns A buffer containing the PDF data or null if PDF generation is disabled
 */
export async function generatePdf(cv: CV, files: any[]): Promise<Buffer | null> {
  // Check if PDF export is enabled
  if (!isPdfExportEnabled()) {
    return null;
  }

  try {
    // Parse the CV data
    const personalInfo = (cv.personalInfo as unknown as PersonalInfo) || {};
    const education = (cv.education as unknown as EducationEntry[]) || [];
    const workExperience = (cv.workExperience as unknown as WorkExperienceEntry[]) || [];
    const skills = (cv.skills as unknown as Skill[]) || [];
    const references = (cv.references as unknown as Reference[]) || [];

    // Get cover letter if available
    const coverLetter = cv.coverLetter ?
      (typeof cv.coverLetter === 'string' ?
        { content: cv.coverLetter } :
        (cv.coverLetter as unknown as CoverLetter)) :
      undefined;

    // Create a new PDF document
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
    const { width, height } = page.getSize();

    // Get fonts
    const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
    const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

    // Set margins
    const margin = 50;
    const contentWidth = width - 2 * margin;

    // Add title - use German for German CVs
    const title = cv.template === 'german-ausbildung' || cv.language === 'de'
      ? 'LEBENSLAUF'
      : 'CURRICULUM VITAE';

    page.drawText(title, {
      x: margin,
      y: height - margin,
      size: 16,
      font: boldFont,
    });

    // Add personal info
    let yPosition = height - margin - 40;

    page.drawText(`${personalInfo.firstName} ${personalInfo.lastName}`, {
      x: margin,
      y: yPosition,
      size: 14,
      font: boldFont,
    });

    yPosition -= 20;

    if (personalInfo.email) {
      const emailLabel = cv.template === 'german-ausbildung' || cv.language === 'de'
        ? 'E-Mail:'
        : 'Email:';

      page.drawText(`${emailLabel} ${personalInfo.email}`, {
        x: margin,
        y: yPosition,
        size: 10,
        font: font,
      });
      yPosition -= 15;
    }

    if (personalInfo.phone) {
      const phoneLabel = cv.template === 'german-ausbildung' || cv.language === 'de'
        ? 'Telefon:'
        : 'Phone:';

      page.drawText(`${phoneLabel} ${personalInfo.phone}`, {
        x: margin,
        y: yPosition,
        size: 10,
        font: font,
      });
      yPosition -= 15;
    }

    if (personalInfo.address) {
      const addressLabel = cv.template === 'german-ausbildung' || cv.language === 'de'
        ? 'Adresse:'
        : 'Address:';

      page.drawText(`${addressLabel} ${personalInfo.address}, ${personalInfo.postalCode} ${personalInfo.city}`, {
        x: margin,
        y: yPosition,
        size: 10,
        font: font,
      });
      yPosition -= 15;
    }

    // Add education section - use German for German CVs
    yPosition -= 20;
    const educationTitle = cv.template === 'german-ausbildung' || cv.language === 'de'
      ? 'AUSBILDUNG'
      : 'EDUCATION';

    page.drawText(educationTitle, {
      x: margin,
      y: yPosition,
      size: 12,
      font: boldFont,
    });

    yPosition -= 15;

    for (const entry of education) {
      const presentText = cv.template === 'german-ausbildung' || cv.language === 'de'
        ? 'Heute'
        : 'Present';

      const dateText = `${entry.startDate.substring(0, 7)} - ${entry.current ? presentText : entry.endDate ? entry.endDate.substring(0, 7) : ''}`;

      page.drawText(dateText, {
        x: margin,
        y: yPosition,
        size: 10,
        font: font,
      });

      yPosition -= 15;

      page.drawText(`${entry.degree} in ${entry.field}`, {
        x: margin,
        y: yPosition,
        size: 10,
        font: boldFont,
      });

      yPosition -= 15;

      page.drawText(`${entry.institution}, ${entry.location}`, {
        x: margin,
        y: yPosition,
        size: 10,
        font: font,
      });

      if (entry.description) {
        yPosition -= 15;
        page.drawText(entry.description, {
          x: margin,
          y: yPosition,
          size: 10,
          font: font,
          maxWidth: contentWidth,
        });
      }

      yPosition -= 20;
    }

    // Add work experience section - use German for German CVs
    yPosition -= 10;
    const workExperienceTitle = cv.template === 'german-ausbildung' || cv.language === 'de'
      ? 'BERUFSERFAHRUNG'
      : 'WORK EXPERIENCE';

    page.drawText(workExperienceTitle, {
      x: margin,
      y: yPosition,
      size: 12,
      font: boldFont,
    });

    yPosition -= 15;

    for (const entry of workExperience) {
      const presentText = cv.template === 'german-ausbildung' || cv.language === 'de'
        ? 'Heute'
        : 'Present';

      const dateText = `${entry.startDate.substring(0, 7)} - ${entry.current ? presentText : entry.endDate ? entry.endDate.substring(0, 7) : ''}`;

      page.drawText(dateText, {
        x: margin,
        y: yPosition,
        size: 10,
        font: font,
      });

      yPosition -= 15;

      page.drawText(`${entry.position}`, {
        x: margin,
        y: yPosition,
        size: 10,
        font: boldFont,
      });

      yPosition -= 15;

      page.drawText(`${entry.company}, ${entry.location}`, {
        x: margin,
        y: yPosition,
        size: 10,
        font: font,
      });

      if (entry.description) {
        yPosition -= 15;
        page.drawText(entry.description, {
          x: margin,
          y: yPosition,
          size: 10,
          font: font,
          maxWidth: contentWidth,
        });
      }

      yPosition -= 20;
    }

    // Add skills section - use German for German CVs
    if (skills.length > 0) {
      yPosition -= 10;
      const skillsTitle = cv.template === 'german-ausbildung' || cv.language === 'de'
        ? 'FÄHIGKEITEN'
        : 'SKILLS';

      page.drawText(skillsTitle, {
        x: margin,
        y: yPosition,
        size: 12,
        font: boldFont,
      });

      yPosition -= 15;

      // Group skills by category
      const technicalSkills = skills.filter(skill => skill.category === 'technical');
      const languageSkills = skills.filter(skill => skill.category === 'language');
      const softSkills = skills.filter(skill => skill.category === 'soft');

      if (technicalSkills.length > 0) {
        const technicalSkillsTitle = cv.template === 'german-ausbildung' || cv.language === 'de'
          ? 'Technische Fähigkeiten:'
          : 'Technical Skills:';

        page.drawText(technicalSkillsTitle, {
          x: margin,
          y: yPosition,
          size: 10,
          font: boldFont,
        });

        yPosition -= 15;

        page.drawText(technicalSkills.map(skill => skill.name).join(', '), {
          x: margin,
          y: yPosition,
          size: 10,
          font: font,
          maxWidth: contentWidth,
        });

        yPosition -= 20;
      }

      if (languageSkills.length > 0) {
        const languageSkillsTitle = cv.template === 'german-ausbildung' || cv.language === 'de'
          ? 'Sprachkenntnisse:'
          : 'Language Skills:';

        page.drawText(languageSkillsTitle, {
          x: margin,
          y: yPosition,
          size: 10,
          font: boldFont,
        });

        yPosition -= 15;

        page.drawText(languageSkills.map(skill => skill.name).join(', '), {
          x: margin,
          y: yPosition,
          size: 10,
          font: font,
          maxWidth: contentWidth,
        });

        yPosition -= 20;
      }

      if (softSkills.length > 0) {
        const softSkillsTitle = cv.template === 'german-ausbildung' || cv.language === 'de'
          ? 'Soziale Kompetenzen:'
          : 'Soft Skills:';

        page.drawText(softSkillsTitle, {
          x: margin,
          y: yPosition,
          size: 10,
          font: boldFont,
        });

        yPosition -= 15;

        page.drawText(softSkills.map(skill => skill.name).join(', '), {
          x: margin,
          y: yPosition,
          size: 10,
          font: font,
          maxWidth: contentWidth,
        });

        yPosition -= 20;
      }
    }

    // Add references section if available - use German for German CVs
    if (references.length > 0) {
      yPosition -= 10;
      const referencesTitle = cv.template === 'german-ausbildung' || cv.language === 'de'
        ? 'REFERENZEN'
        : 'REFERENCES';

      page.drawText(referencesTitle, {
        x: margin,
        y: yPosition,
        size: 12,
        font: boldFont,
      });

      yPosition -= 15;

      for (const reference of references) {
        page.drawText(`${reference.name}`, {
          x: margin,
          y: yPosition,
          size: 10,
          font: boldFont,
        });

        yPosition -= 15;

        page.drawText(`${reference.position}, ${reference.company}`, {
          x: margin,
          y: yPosition,
          size: 10,
          font: font,
        });

        yPosition -= 15;

        if (reference.email || reference.phone) {
          const contactLabel = cv.template === 'german-ausbildung' || cv.language === 'de'
            ? 'Kontakt:'
            : 'Contact:';

          page.drawText(`${contactLabel} ${reference.email || ''} ${reference.phone ? '| ' + reference.phone : ''}`, {
            x: margin,
            y: yPosition,
            size: 10,
            font: font,
          });

          yPosition -= 15;
        }

        yPosition -= 10;
      }
    }

    // Add cover letter if available
    if (coverLetter) {
      // Add a new page for the cover letter
      const coverLetterPage = pdfDoc.addPage([595.28, 841.89]); // A4 size
      const { width: clWidth, height: clHeight } = coverLetterPage.getSize();

      // Add title - use German for German CVs
      const coverLetterTitle = cv.template === 'german-ausbildung' || cv.language === 'de'
        ? 'ANSCHREIBEN'
        : 'COVER LETTER';

      coverLetterPage.drawText(coverLetterTitle, {
        x: margin,
        y: clHeight - margin,
        size: 16,
        font: boldFont,
      });

      let clYPosition = clHeight - margin - 40;

      // Add sender information
      if (cv.template === 'german-ausbildung' || cv.language === 'de') {
        coverLetterPage.drawText(`${personalInfo.firstName} ${personalInfo.lastName} · ${personalInfo.address} · ${personalInfo.postalCode} ${personalInfo.city}`, {
          x: margin,
          y: clYPosition,
          size: 8,
          font: font,
        });

        clYPosition -= 20;
      }

      // Add recipient information if available
      if (typeof coverLetter === 'object' && (coverLetter.recipientName || coverLetter.recipientCompany)) {
        if (coverLetter.recipientName) {
          coverLetterPage.drawText(coverLetter.recipientName, {
            x: margin,
            y: clYPosition,
            size: 10,
            font: font,
          });

          clYPosition -= 15;
        }

        if (coverLetter.recipientCompany) {
          coverLetterPage.drawText(coverLetter.recipientCompany, {
            x: margin,
            y: clYPosition,
            size: 10,
            font: font,
          });

          clYPosition -= 15;
        }

        if (coverLetter.recipientAddress) {
          coverLetterPage.drawText(coverLetter.recipientAddress, {
            x: margin,
            y: clYPosition,
            size: 10,
            font: font,
          });

          clYPosition -= 15;
        }

        if (coverLetter.recipientPostalCode || coverLetter.recipientCity) {
          coverLetterPage.drawText(`${coverLetter.recipientPostalCode || ''} ${coverLetter.recipientCity || ''}`, {
            x: margin,
            y: clYPosition,
            size: 10,
            font: font,
          });

          clYPosition -= 15;
        }

        clYPosition -= 20;
      }

      // Add date
      const currentDate = new Date().toLocaleDateString(
        cv.template === 'german-ausbildung' || cv.language === 'de' ? 'de-DE' : 'en-US',
        { year: 'numeric', month: 'long', day: 'numeric' }
      );

      const dateText = typeof coverLetter === 'object' && coverLetter.date ? coverLetter.date : currentDate;

      coverLetterPage.drawText(dateText, {
        x: clWidth - margin - 150,
        y: clYPosition,
        size: 10,
        font: font,
      });

      clYPosition -= 30;

      // Add subject if available
      if (typeof coverLetter === 'object' && coverLetter.subject) {
        coverLetterPage.drawText(coverLetter.subject, {
          x: margin,
          y: clYPosition,
          size: 12,
          font: boldFont,
        });

        clYPosition -= 30;
      }

      // Add salutation
      let salutation = 'Sehr geehrte Damen und Herren,';

      if (typeof coverLetter === 'object') {
        if (coverLetter.salutation) {
          salutation = coverLetter.salutation;
        } else if (coverLetter.recipientName) {
          if (coverLetter.recipientName.includes('Herr')) {
            salutation = 'Sehr geehrter Herr ' + coverLetter.recipientName.split('Herr ')[1] + ',';
          } else if (coverLetter.recipientName.includes('Frau')) {
            salutation = 'Sehr geehrte Frau ' + coverLetter.recipientName.split('Frau ')[1] + ',';
          }
        }
      }

      coverLetterPage.drawText(salutation, {
        x: margin,
        y: clYPosition,
        size: 10,
        font: font,
      });

      clYPosition -= 30;

      // Add content
      const content = typeof coverLetter === 'object' && coverLetter.content ?
        coverLetter.content :
        typeof coverLetter === 'string' ?
          coverLetter :
          '';

      // Split content into lines to fit on page
      const contentLines = content.split('\n');
      let currentPage = coverLetterPage;

      for (const line of contentLines) {
        // Check if we need a new page
        if (clYPosition < 100) {
          // Add a new page
          currentPage = pdfDoc.addPage([595.28, 841.89]);
          clYPosition = clHeight - margin;
        }

        // Split long lines
        const words = line.split(' ');
        let currentLine = '';

        for (const word of words) {
          if ((currentLine + ' ' + word).length <= 80) {
            currentLine += (currentLine ? ' ' : '') + word;
          } else {
            // Draw the current line
            currentPage.drawText(currentLine, {
              x: margin,
              y: clYPosition,
              size: 10,
              font: font,
            });

            clYPosition -= 15;

            // Check if we need a new page
            if (clYPosition < 100) {
              // Add a new page
              currentPage = pdfDoc.addPage([595.28, 841.89]);
              clYPosition = clHeight - margin;
            }

            // Start a new line with the current word
            currentLine = word;
          }
        }

        // Draw the last line
        if (currentLine) {
          currentPage.drawText(currentLine, {
            x: margin,
            y: clYPosition,
            size: 10,
            font: font,
          });

          clYPosition -= 15;
        } else {
          // Empty line (paragraph break)
          clYPosition -= 15;
        }
      }

      // Add closing
      clYPosition -= 20;

      const closingText = cv.template === 'german-ausbildung' || cv.language === 'de'
        ? 'Mit freundlichen Grüßen,'
        : 'Sincerely,';

      currentPage.drawText(closingText, {
        x: margin,
        y: clYPosition,
        size: 10,
        font: font,
      });

      clYPosition -= 50; // Space for signature

      currentPage.drawText(`${personalInfo.firstName} ${personalInfo.lastName}`, {
        x: margin,
        y: clYPosition,
        size: 10,
        font: font,
      });
    }

    // Add footer with page number - use German for German CVs
    const pageCount = pdfDoc.getPageCount();
    const pageText = cv.template === 'german-ausbildung' || cv.language === 'de'
      ? `Seite 1 von ${pageCount}`
      : `Page 1 of ${pageCount}`;

    page.drawText(`${cv.title} - ${pageText}`, {
      x: width / 2 - 50,
      y: 30,
      size: 8,
      font: font,
    });

    // Add page numbers to all pages
    for (let i = 1; i < pageCount; i++) {
      const currentPage = pdfDoc.getPage(i);
      const currentPageText = cv.template === 'german-ausbildung' || cv.language === 'de'
        ? `Seite ${i + 1} von ${pageCount}`
        : `Page ${i + 1} of ${pageCount}`;

      currentPage.drawText(`${cv.title} - ${currentPageText}`, {
        x: width / 2 - 50,
        y: 30,
        size: 8,
        font: font,
      });
    }

    // Serialize the PDF to bytes
    const pdfBytes = await pdfDoc.save();

    // Check if there are any certificates to merge
    const { urls: certificateUrls, entries: certificateEntries } = extractCertificatesWithEntries(education);

    if (certificateUrls.length > 0) {
      console.log(`Found ${certificateUrls.length} certificates to merge in fallback PDF service`);
      try {
        // Show progress message
        console.log('Starting certificate merging process in fallback service...');

        // Merge certificates with the main PDF, passing education entries for titles
        const mergedBuffer = await mergePdfs(pdfBytes, certificateUrls, certificateEntries);

        console.log('Successfully merged certificates with main PDF in fallback service');
        return mergedBuffer;
      } catch (mergeError) {
        console.error('Error merging certificates in fallback service:', mergeError);
        // If merging fails, return the original buffer
        console.log('Returning original PDF without certificates');
        return Buffer.from(pdfBytes);
      }
    } else {
      // No certificates to merge, return the original buffer
      return Buffer.from(pdfBytes);
    }
  } catch (error) {
    console.error('PDF generation error:', error);

    // If we can't generate a PDF with pdf-lib, create a simple text-based PDF
    try {
      const pdfContent = `
        CV: ${cv.title}
        Template: ${cv.template}
        Language: ${cv.language}

        This is a placeholder PDF. In a real implementation, this would be a properly formatted CV.
      `;

      // Convert the string to a buffer
      const buffer = Buffer.from(pdfContent);

      return buffer;
    } catch (fallbackError) {
      console.error('Fallback PDF generation error:', fallbackError);
      throw new Error('Failed to generate PDF');
    }
  }
}
