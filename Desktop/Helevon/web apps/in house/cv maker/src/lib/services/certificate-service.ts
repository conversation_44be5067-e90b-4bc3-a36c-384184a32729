import { prisma } from '@/lib/db/prisma';

/**
 * Service for managing certificates in the database
 */
export class CertificateService {
  /**
   * Save a certificate to the database
   */
  static async saveCertificate(data: {
    userId: string;
    educationEntryId?: number;
    fileName: string;
    fileType: string;
    fileSize: number;
    fileData: string; // Base64 encoded PDF
  }) {
    try {
      const certificate = await prisma.certificate.create({
        data: {
          userId: data.userId,
          educationEntryId: data.educationEntryId,
          fileName: data.fileName,
          fileType: data.fileType,
          fileSize: data.fileSize,
          fileData: data.fileData,
        }
      });

      return certificate;
    } catch (error) {
      console.error('Error saving certificate:', error);
      throw new Error('Failed to save certificate');
    }
  }

  /**
   * Get a certificate by ID
   */
  static async getCertificateById(id: number) {
    try {
      const certificate = await prisma.certificate.findFirst({
        where: {
          id: id,
          isDeleted: false
        },
      });

      return certificate;
    } catch (error) {
      console.error('Error getting certificate:', error);
      throw new Error('Failed to get certificate');
    }
  }

  /**
   * Get certificates for an education entry
   */
  static async getCertificatesByEducationEntryId(educationEntryId: number) {
    try {
      const results = await prisma.certificate.findMany({
        where: {
          educationEntryId: educationEntryId,
          isDeleted: false
        },
      });

      return results;
    } catch (error) {
      console.error('Error getting certificates:', error);
      throw new Error('Failed to get certificates');
    }
  }

  /**
   * Delete a certificate (soft delete)
   */
  static async deleteCertificate(id: number, userId: string) {
    try {
      const certificate = await prisma.certificate.update({
        where: {
          id: id,
          userId: userId
        },
        data: {
          isDeleted: true
        }
      });

      return certificate;
    } catch (error) {
      console.error('Error deleting certificate:', error);
      throw new Error('Failed to delete certificate');
    }
  }

  /**
   * Hard delete a certificate
   */
  static async hardDeleteCertificate(id: number, userId: string) {
    try {
      const certificate = await prisma.certificate.delete({
        where: {
          id: id,
          userId: userId
        }
      });

      return certificate;
    } catch (error) {
      console.error('Error hard deleting certificate:', error);
      throw new Error('Failed to hard delete certificate');
    }
  }
}
