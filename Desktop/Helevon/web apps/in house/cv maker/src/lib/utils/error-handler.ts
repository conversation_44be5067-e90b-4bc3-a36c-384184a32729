/**
 * Global error handler for consistent error handling and logging
 */

import { metrics } from '@/app/api/metrics/route';
import { ZodError } from 'zod';

// Error types
export enum ErrorType {
  VALIDATION = 'VALIDATION_ERROR',
  AUTHENTICATION = 'AUTHENTICATION_ERROR',
  AUTHORIZATION = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND_ERROR',
  DATABASE = 'DATABASE_ERROR',
  SERVER = 'SERVER_ERROR',
  EXTERNAL_SERVICE = 'EXTERNAL_SERVICE_ERROR',
}

// Error interface
export interface AppError extends Error {
  type: ErrorType;
  statusCode: number;
  details?: any;
}

// Create a structured error
export function createError(
  message: string,
  type: ErrorType,
  statusCode: number,
  details?: any
): AppError {
  const error = new Error(message) as AppError;
  error.type = type;
  error.statusCode = statusCode;
  error.details = details;
  return error;
}

// Handle errors consistently
export function handleError(error: unknown): { message: string; statusCode: number; details?: any } {
  // Increment error count for metrics
  metrics.errorCount++;
  
  // Log the error
  console.error('Application error:', error);
  
  // Handle known error types
  if ((error as AppError).type) {
    const appError = error as AppError;
    return {
      message: appError.message,
      statusCode: appError.statusCode,
      details: appError.details,
    };
  }
  
  // Handle Zod validation errors
  if (error instanceof ZodError) {
    return {
      message: 'Validation error',
      statusCode: 400,
      details: error.errors,
    };
  }
  
  // Handle Prisma errors
  if ((error as any).code?.startsWith('P')) {
    return {
      message: 'Database error',
      statusCode: 500,
      details: process.env.NODE_ENV === 'development' ? (error as any).message : undefined,
    };
  }
  
  // Handle generic errors
  return {
    message: (error as Error)?.message || 'An unexpected error occurred',
    statusCode: 500,
  };
}

// Utility to safely parse JSON
export function safeJsonParse<T>(json: string, fallback: T): T {
  try {
    return JSON.parse(json) as T;
  } catch (error) {
    console.error('JSON parse error:', error);
    return fallback;
  }
}
