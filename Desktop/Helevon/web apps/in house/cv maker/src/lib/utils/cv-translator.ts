import { PersonalInfo, EducationEntry, WorkExperienceEntry, Skill, Reference } from '@/types/cv';

// German translations for common CV terms
const translations = {
  // Skill categories
  technical: 'Technisch',
  language: 'Sprache',
  soft: 'Soft Skills',
  
  // Common terms
  present: 'Heute',
  
  // Common skill names
  english: 'Englisch',
  german: 'Deutsch',
  arabic: 'Arabisch',
  french: 'Französisch',
  spanish: 'Spanisch',
  italian: 'Italienisch',
  russian: 'Russisch',
  chinese: 'Chinesisch',
  japanese: 'Japanisch',
  
  // Technical skills
  programming: 'Programmierung',
  javascript: 'JavaScript',
  typescript: 'TypeScript',
  react: 'React',
  angular: 'Angular',
  vue: 'Vue.js',
  node: 'Node.js',
  python: 'Python',
  java: 'Java',
  csharp: 'C#',
  cpp: 'C++',
  php: 'PHP',
  ruby: 'Ruby',
  swift: 'Swift',
  kotlin: 'Kotl<PERSON>',
  sql: 'SQL',
  mongodb: 'MongoDB',
  postgresql: 'PostgreSQL',
  mysql: 'MySQL',
  
  // Soft skills
  teamwork: 'Teamarbeit',
  communication: 'Kommunikation',
  leadership: 'Führungsqualitäten',
  problemsolving: 'Problemlösung',
  creativity: 'Kreativität',
  adaptability: 'Anpassungsfähigkeit',
  timemanagement: 'Zeitmanagement',
  criticalthinking: 'Kritisches Denken',
  
  // Education degrees
  bachelor: 'Bachelor',
  master: 'Master',
  phd: 'Doktor',
  diploma: 'Diplom',
  certificate: 'Zertifikat',
  
  // Education fields
  computerscience: 'Informatik',
  engineering: 'Ingenieurwesen',
  business: 'Betriebswirtschaft',
  economics: 'Wirtschaftswissenschaften',
  medicine: 'Medizin',
  law: 'Rechtswissenschaften',
  
  // Job titles
  developer: 'Entwickler',
  engineer: 'Ingenieur',
  manager: 'Manager',
  director: 'Direktor',
  assistant: 'Assistent',
  consultant: 'Berater',
  analyst: 'Analyst',
  specialist: 'Spezialist',
  
  // Countries
  germany: 'Deutschland',
  austria: 'Österreich',
  switzerland: 'Schweiz',
  usa: 'USA',
  uk: 'Großbritannien',
  france: 'Frankreich',
  spain: 'Spanien',
  italy: 'Italien',
  
  // Relationship types
  manager: 'Vorgesetzter',
  colleague: 'Kollege',
  client: 'Kunde',
  professor: 'Professor',
  supervisor: 'Betreuer',
};

/**
 * Attempts to translate a term to German
 * Falls back to the original term if no translation is found
 */
export function translateTerm(term: string): string {
  if (!term) return '';
  
  // Convert to lowercase and remove spaces for lookup
  const normalizedTerm = term.toLowerCase().replace(/\\s+/g, '');
  
  // Check if we have a direct translation
  if (normalizedTerm in translations) {
    return translations[normalizedTerm as keyof typeof translations];
  }
  
  // No translation found, return original
  return term;
}

/**
 * Translates personal information to German
 */
export function translatePersonalInfo(personalInfo: PersonalInfo): PersonalInfo {
  // Personal info fields are kept as is, only the labels in the template are translated
  return { ...personalInfo };
}

/**
 * Translates education entries to German
 */
export function translateEducation(education: EducationEntry[]): EducationEntry[] {
  return education.map(entry => ({
    ...entry,
    degree: translateTerm(entry.degree),
    field: translateTerm(entry.field),
    // Institution and location are kept as is
  }));
}

/**
 * Translates work experience entries to German
 */
export function translateWorkExperience(workExperience: WorkExperienceEntry[]): WorkExperienceEntry[] {
  return workExperience.map(entry => ({
    ...entry,
    position: translateTerm(entry.position),
    // Company and location are kept as is
  }));
}

/**
 * Translates skills to German
 */
export function translateSkills(skills: Skill[]): Skill[] {
  return skills.map(skill => ({
    ...skill,
    name: translateTerm(skill.name),
    category: skill.category === 'technical' 
      ? 'technical' 
      : skill.category === 'language' 
        ? 'language' 
        : 'soft',
  }));
}

/**
 * Translates references to German
 */
export function translateReferences(references: Reference[]): Reference[] {
  return references.map(reference => ({
    ...reference,
    position: translateTerm(reference.position),
    relationship: translateTerm(reference.relationship),
    // Name and company are kept as is
  }));
}

/**
 * Translates the entire CV content to German
 */
export function translateCV({
  personalInfo,
  education,
  workExperience,
  skills,
  references,
}: {
  personalInfo: PersonalInfo;
  education: EducationEntry[];
  workExperience: WorkExperienceEntry[];
  skills: Skill[];
  references: Reference[];
}) {
  return {
    personalInfo: translatePersonalInfo(personalInfo),
    education: translateEducation(education),
    workExperience: translateWorkExperience(workExperience),
    skills: translateSkills(skills),
    references: translateReferences(references),
  };
}
