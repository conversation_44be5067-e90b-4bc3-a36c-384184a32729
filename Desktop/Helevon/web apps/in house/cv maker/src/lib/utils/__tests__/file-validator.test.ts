import { validateFileUpload, sanitizeFilename } from '../file-validator';

describe('File Validator', () => {
  // Mock environment variable
  const originalEnv = process.env;

  beforeEach(() => {
    jest.resetModules();
    process.env = { ...originalEnv };
    process.env.MAX_UPLOAD_SIZE_MB = '5';
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('validateFileUpload', () => {
    it('should validate a valid PDF file', () => {
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB

      const result = validateFileUpload(file);
      expect(result.valid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should validate a valid image file', () => {
      const file = new File(['test content'], 'test.jpg', { type: 'image/jpeg' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB

      const result = validateFileUpload(file);
      expect(result.valid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    it('should reject a file that is too large', () => {
      const file = new File(['test content'], 'test.pdf', { type: 'application/pdf' });
      Object.defineProperty(file, 'size', { value: 6 * 1024 * 1024 }); // 6MB

      const result = validateFileUpload(file);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('exceeds the maximum limit');
    });

    it('should reject a file with an invalid type', () => {
      const file = new File(['test content'], 'test.exe', { type: 'application/x-msdownload' });
      Object.defineProperty(file, 'size', { value: 1024 * 1024 }); // 1MB

      const result = validateFileUpload(file);
      expect(result.valid).toBe(false);
      expect(result.error).toContain('Only PDF and image files are allowed');
    });
  });

  describe('sanitizeFilename', () => {
    it('should remove path components from filename', () => {
      expect(sanitizeFilename('/path/to/file.pdf')).toBe('file.pdf');
      expect(sanitizeFilename('C:\\path\\to\\file.pdf')).toBe('file.pdf');
    });

    it('should remove dangerous characters from filename', () => {
      expect(sanitizeFilename('file<script>.pdf')).toBe('file_script_.pdf');
      // The implementation removes path components first, so only '.pdf' remains
      // Then it replaces non-word characters, resulting in '.pdf'
      expect(sanitizeFilename('file;rm -rf /.pdf')).toBe('.pdf');
    });

    it('should preserve safe characters in filename', () => {
      expect(sanitizeFilename('safe-file_name.pdf')).toBe('safe-file_name.pdf');
      expect(sanitizeFilename('file with spaces.pdf')).toBe('file with spaces.pdf');
    });
  });
});
