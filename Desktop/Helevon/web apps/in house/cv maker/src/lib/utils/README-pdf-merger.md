# PDF Certificate Embedding

This feature allows PDF certificates to be automatically embedded into the final CV PDF export. The certificates are appended to the end of the CV document in their original format, without any reformatting.

## How It Works

1. When a user uploads a certificate (PDF file) and associates it with an education entry, the certificate URL is stored in the education entry.
2. When the user exports their CV as a PDF, the PDF generation service:
   - Renders the CV template to a PDF
   - Extracts all certificate URLs from the education entries
   - Fetches each certificate PDF
   - Merges all certificate PDFs with the main CV PDF
   - Returns the merged PDF to the user

## Implementation Details

The implementation uses the `pdf-lib` library to handle PDF merging. The main components are:

1. `pdf-merger.ts` - Utility functions for fetching and merging PDFs
2. `react-pdf-service.ts` - Main PDF generation service that uses React PDF for rendering
3. `pdf-service.ts` - Fallback PDF generation service that uses pdf-lib directly

## Testing

You can test the PDF merging functionality using the test script:

```bash
node src/scripts/test-pdf-merger.js [path-to-certificate.pdf]
```

This will create two files in the `public` directory:
- `test-main.pdf` - A simple test PDF
- `test-merged.pdf` - The test PDF merged with the certificate

## Troubleshooting

If certificates are not being embedded correctly, check the following:

1. Make sure the certificate URL is correct and accessible
2. Check the server logs for any errors during PDF merging
3. Verify that the certificate is a valid PDF file
4. Ensure that the `CAN_EXPORT` environment variable is set to `true`

## Limitations

- Only PDF certificates are supported for embedding
- Image certificates (JPEG, PNG) are displayed in the CV but not embedded in the final PDF
- Very large PDF files may cause performance issues
