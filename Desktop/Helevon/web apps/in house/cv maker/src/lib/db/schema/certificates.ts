import { pgTable, serial, text, timestamp, varchar, integer, boolean } from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { users } from './users';
import { educationEntries } from './education';

/**
 * Schema for storing certificates and other uploaded files
 * This is a placeholder to satisfy imports, as we're using Prisma for database access
 */
export const certificates = pgTable('certificates', {
  id: serial('id').primaryKey(),
  userId: varchar('user_id', { length: 255 }).notNull().references(() => users.id, { onDelete: 'cascade' }),
  educationEntryId: integer('education_entry_id').references(() => educationEntries.id, { onDelete: 'cascade' }),
  fileName: varchar('file_name', { length: 255 }).notNull(),
  fileType: varchar('file_type', { length: 100 }).notNull(),
  fileSize: integer('file_size').notNull(),
  fileData: text('file_data').notNull(), // Store PDF as base64 encoded string
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  isDeleted: boolean('is_deleted').default(false).notNull(),
});

export const certificatesRelations = relations(certificates, ({ one }) => ({
  user: one(users, {
    fields: [certificates.userId],
    references: [users.id],
  }),
  educationEntry: one(educationEntries, {
    fields: [certificates.educationEntryId],
    references: [educationEntries.id],
  }),
}));
