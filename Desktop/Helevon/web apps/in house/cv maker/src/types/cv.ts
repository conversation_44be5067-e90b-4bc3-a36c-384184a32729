export interface PersonalInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  postalCode: string;
  country: string;
  dateOfBirth?: string;
  placeOfBirth?: string;
  nationality?: string;
  maritalStatus?: string;
  photoUrl?: string;
}

export interface EducationEntry {
  id: string;
  institution: string;
  degree: string;
  field: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  location: string;
  description?: string;
  certificateUrl?: string;
}

export interface WorkExperienceEntry {
  id: string;
  company: string;
  position: string;
  startDate: string;
  endDate?: string;
  current: boolean;
  location: string;
  description?: string;
}

export interface Skill {
  id: string;
  name: string;
  level: number; // 1-5
  category: 'technical' | 'language' | 'soft';
}

export interface Reference {
  id: string;
  name: string;
  company: string;
  position: string;
  email?: string;
  phone?: string;
  relationship: string;
}

export interface Certificate {
  id: string;
  name: string;
  issuer: string;
  date: string;
  fileUrl?: string;
  description?: string;
}

export interface CoverLetter {
  // Recipient information
  recipientName?: string;
  recipientCompany?: string;
  recipientAddress?: string;
  recipientCity?: string;
  recipientPostalCode?: string; // Always 5 numbers in Germany
  recipientCountry?: string;
  recipientEmail?: string;
  recipientPhone?: string;
  recipientOtherInfo?: string;

  // Cover letter content
  subject?: string; // Will be used as the title on the cover page if provided
  content?: string;
  date?: string;
  salutation?: string; // Auto-generated based on recipientName if provided

  // Signature
  signatureType?: 'text' | 'image'; // Whether to use text or uploaded image
  signatureImageUrl?: string; // URL to uploaded signature image
}

export interface CVData {
  personalInfo: PersonalInfo;
  education: EducationEntry[];
  workExperience: WorkExperienceEntry[];
  skills: Skill[];
  references: Reference[];
  certificates: Certificate[];
  coverLetter?: string | CoverLetter;
}

export type CVTemplate = 'standard' | 'modern' | 'creative' | 'german-ausbildung';

export interface CV {
  id: string;
  userId: string;
  title: string;
  template: CVTemplate;
  language: string;
  personalInfo: PersonalInfo;
  education: EducationEntry[];
  workExperience: WorkExperienceEntry[];
  skills: Skill[];
  references: Reference[];
  certificates: Certificate[];
  coverLetter?: string | CoverLetter;
  createdAt: string;
  updatedAt: string;
}

export interface FileUpload {
  id: string;
  userId: string;
  cvId?: string;
  name: string;
  type: string;
  size: number;
  url: string;
  category: 'photo' | 'certificate' | 'cover_letter' | 'other';
  createdAt: string;
  updatedAt: string;
}
