'use client';

import { useState, useEffect } from 'react';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, AlertCircle, FileText, Loader2 } from 'lucide-react';

export interface PdfExportProgressProps {
  isExporting: boolean;
  error: string | null;
  certificateCount: number;
  onComplete?: () => void;
}

export function PdfExportProgress({
  isExporting,
  error,
  certificateCount,
  onComplete
}: PdfExportProgressProps) {
  const [progress, setProgress] = useState(0);
  const [stage, setStage] = useState<'preparing' | 'rendering' | 'merging' | 'complete' | 'error'>('preparing');
  const [stageText, setStageText] = useState('Preparing PDF export...');

  // Simulate progress based on the export state
  useEffect(() => {
    if (!isExporting) {
      if (error) {
        setStage('error');
        setProgress(100);
        return;
      }
      
      if (progress >= 100) {
        setStage('complete');
        if (onComplete) {
          setTimeout(() => {
            onComplete();
          }, 1500);
        }
        return;
      }
      
      return;
    }

    let interval: NodeJS.Timeout;
    
    // Reset progress when export starts
    if (progress === 100) {
      setProgress(0);
      setStage('preparing');
    }
    
    // Simulate progress through different stages
    interval = setInterval(() => {
      setProgress(prevProgress => {
        // Different stages of PDF generation
        if (prevProgress < 30) {
          setStage('preparing');
          setStageText('Preparing PDF export...');
          return prevProgress + 1;
        } else if (prevProgress < 60) {
          setStage('rendering');
          setStageText('Rendering CV template...');
          return prevProgress + 0.5;
        } else if (prevProgress < 95) {
          setStage('merging');
          setStageText(certificateCount > 0 
            ? `Merging ${certificateCount} certificate${certificateCount > 1 ? 's' : ''}...` 
            : 'Finalizing PDF...');
          return prevProgress + 0.2;
        } else if (prevProgress < 100) {
          return prevProgress + 0.1;
        } else {
          clearInterval(interval);
          setStage('complete');
          setStageText('PDF export complete!');
          if (onComplete) {
            setTimeout(() => {
              onComplete();
            }, 1500);
          }
          return 100;
        }
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isExporting, progress, error, certificateCount, onComplete]);

  if (!isExporting && !error && progress !== 100) {
    return null;
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg max-w-md w-full">
        {stage === 'error' ? (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error || 'An error occurred during PDF export.'}
            </AlertDescription>
          </Alert>
        ) : stage === 'complete' ? (
          <Alert variant="default" className="mb-4 border-green-500 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertTitle className="text-green-700">Success</AlertTitle>
            <AlertDescription className="text-green-600">
              PDF export completed successfully!
            </AlertDescription>
          </Alert>
        ) : (
          <div className="flex items-center mb-4">
            {stage === 'preparing' && <FileText className="mr-2 h-5 w-5 text-blue-500 animate-pulse" />}
            {stage === 'rendering' && <Loader2 className="mr-2 h-5 w-5 text-blue-500 animate-spin" />}
            {stage === 'merging' && <FileText className="mr-2 h-5 w-5 text-blue-500 animate-pulse" />}
            <h3 className="text-lg font-medium">{stageText}</h3>
          </div>
        )}
        
        <Progress value={progress} className="h-2 mb-2" />
        
        <div className="text-xs text-gray-500 flex justify-between">
          <span>Progress: {Math.round(progress)}%</span>
          {certificateCount > 0 && stage === 'merging' && (
            <span>{certificateCount} certificate{certificateCount > 1 ? 's' : ''} to process</span>
          )}
        </div>
      </div>
    </div>
  );
}
