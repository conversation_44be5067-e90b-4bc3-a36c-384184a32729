import React from 'react';
import { useFormContext, Controller } from 'react-hook-form';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { cn } from '@/lib/utils';

interface FormFieldProps {
  name: string;
  label: string;
  type?: string;
  placeholder?: string;
  required?: boolean;
  className?: string;
  disabled?: boolean;
  description?: string;
  error?: string;
}

export function FormField({
  name,
  label,
  type = 'text',
  placeholder,
  required = false,
  className,
  disabled = false,
  description,
  error,
}: FormFieldProps) {
  const { control } = useFormContext();

  return (
    <div className={cn('space-y-2', className)}>
      <div className="space-y-1">
        <Label htmlFor={name} className="text-sm font-medium">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
        {description && (
          <p className="text-xs text-gray-500 dark:text-gray-400">{description}</p>
        )}
      </div>
      <Controller
        name={name}
        control={control}
        render={({ field, fieldState }) => (
          <>
            <Input
              {...field}
              id={name}
              type={type}
              placeholder={placeholder}
              disabled={disabled}
              className={cn(
                fieldState.error && 'border-red-500 focus-visible:ring-red-500'
              )}
              value={field.value || ''}
            />
            {(fieldState.error || error) && (
              <p className="text-sm text-red-500 mt-1">
                {error || fieldState.error?.message}
              </p>
            )}
          </>
        )}
      />
    </div>
  );
}
