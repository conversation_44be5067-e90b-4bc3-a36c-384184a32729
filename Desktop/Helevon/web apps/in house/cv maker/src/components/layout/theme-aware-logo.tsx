'use client';

import { useTheme } from 'next-themes';
import Image from 'next/image';
import { useEffect, useState } from 'react';

interface ThemeAwareLogoProps {
  className?: string;
  height: number;
  width: number;
}

export function ThemeAwareLogo({ className = '', height, width }: ThemeAwareLogoProps) {
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // After mounting, we have access to the theme
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // During SSR and before hydration, don't render the image to avoid hydration mismatch
    return <div style={{ height, width }} />;
  }

  const currentTheme = theme === 'system' ? resolvedTheme : theme;
  const logoSrc = currentTheme === 'dark'
    ? '/logo/PNG white @3x.png'
    : '/logo/<NAME_EMAIL>';

  return (
    <div className={`relative h-${height} w-${width} ${className}`} style={{ height, width }}>
      <Image
        src={logoSrc}
        alt="Helevon Technology Logo"
        fill
        sizes={`${width * 2}px`}
        className="object-contain"
        priority
      />
    </div>
  );
}
