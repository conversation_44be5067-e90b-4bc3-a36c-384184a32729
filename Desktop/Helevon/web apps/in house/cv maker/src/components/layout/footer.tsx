'use client';

import Link from 'next/link';

export function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="container flex flex-col items-center justify-between gap-4 py-10 md:h-24 md:flex-row md:py-0">
        <div className="flex flex-col items-center gap-4 px-8 md:flex-row md:gap-2 md:px-0">
          <p className="text-center text-sm leading-loose text-muted-foreground md:text-left">
            &copy; {new Date().getFullYear()} CV Maker by 
            <a href='https://github.com/huelight' className='text-sm text-muted-foreground underline underline-offset-4 p-2' target='_blank'>
             Huelight
            </a> 
            for 
            <a href='https://helevon.com' className='text-sm text-muted-foreground underline underline-offset-4 p-2' target='_blank'>
            Helevon Technology LTD.
            </a> 
            All rights reserved.
          </p>
        </div>
        <div className="flex gap-4">
          <Link
            href="/terms"
            className="text-sm text-muted-foreground underline underline-offset-4"
          >
            Terms
          </Link>
          <Link
            href="/privacy"
            className="text-sm text-muted-foreground underline underline-offset-4"
          >
            Privacy
          </Link>
        </div>
      </div>
    </footer>
  );
}
