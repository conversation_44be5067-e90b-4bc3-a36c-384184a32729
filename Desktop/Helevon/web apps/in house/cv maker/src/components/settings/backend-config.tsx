/**
 * Backend Configuration Component
 * 
 * Allows users to configure the backend URL for microservices architecture.
 * Provides health check functionality and connection status.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useAppStore } from '@/lib/stores/app-store';
import { useNotifications } from '@/components/ui/notification-provider';
import { CheckCircle, XCircle, Loader2, RefreshCw } from 'lucide-react';

const backendConfigSchema = z.object({
  backendUrl: z.string().url('Please enter a valid URL'),
});

type BackendConfigFormValues = z.infer<typeof backendConfigSchema>;

export function BackendConfig() {
  const { config, isHealthy, lastHealthCheck, updateConfig, checkHealth } = useAppStore();
  const notifications = useNotifications();
  const [isChecking, setIsChecking] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<BackendConfigFormValues>({
    resolver: zodResolver(backendConfigSchema),
    defaultValues: {
      backendUrl: config.backendUrl,
    },
  });

  // Reset form when config changes
  useEffect(() => {
    reset({ backendUrl: config.backendUrl });
  }, [config.backendUrl, reset]);

  const handleHealthCheck = async () => {
    setIsChecking(true);
    try {
      await checkHealth();
      notifications.success(
        'Health Check Complete',
        isHealthy ? 'Backend is healthy' : 'Backend is not responding'
      );
    } catch (error) {
      notifications.error('Health Check Failed', 'Unable to check backend health');
    } finally {
      setIsChecking(false);
    }
  };

  const onSubmit = async (data: BackendConfigFormValues) => {
    setIsSaving(true);
    try {
      // Update the configuration
      updateConfig({ 
        backendUrl: data.backendUrl,
        isExternalBackend: data.backendUrl !== window.location.origin,
      });

      // Check health of the new backend
      await checkHealth();

      notifications.success(
        'Backend Configuration Updated',
        'The backend URL has been updated successfully'
      );
    } catch (error) {
      notifications.error(
        'Configuration Update Failed',
        'Failed to update backend configuration'
      );
    } finally {
      setIsSaving(false);
    }
  };

  const getHealthStatus = () => {
    if (!lastHealthCheck) {
      return { status: 'unknown', text: 'Not checked', color: 'secondary' };
    }

    const timeSinceCheck = Date.now() - lastHealthCheck;
    const isStale = timeSinceCheck > 5 * 60 * 1000; // 5 minutes

    if (isStale) {
      return { status: 'stale', text: 'Stale', color: 'secondary' };
    }

    return isHealthy
      ? { status: 'healthy', text: 'Healthy', color: 'default' }
      : { status: 'unhealthy', text: 'Unhealthy', color: 'destructive' };
  };

  const healthStatus = getHealthStatus();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          Backend Configuration
          <Badge variant={healthStatus.color as any}>
            {healthStatus.status === 'healthy' && <CheckCircle className="h-3 w-3 mr-1" />}
            {healthStatus.status === 'unhealthy' && <XCircle className="h-3 w-3 mr-1" />}
            {healthStatus.text}
          </Badge>
        </CardTitle>
        <CardDescription>
          Configure the backend service URL for API communication. 
          This allows the frontend to connect to a separate backend service.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="backendUrl">Backend URL</Label>
            <Input
              id="backendUrl"
              placeholder="https://api.example.com"
              {...register('backendUrl')}
            />
            {errors.backendUrl && (
              <p className="text-sm text-red-500">{errors.backendUrl.message}</p>
            )}
            <p className="text-sm text-muted-foreground">
              Enter the base URL of your backend service. Leave as current origin for local backend.
            </p>
          </div>

          <div className="flex gap-2">
            <Button type="submit" disabled={isSaving}>
              {isSaving && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              Save Configuration
            </Button>
            
            <Button
              type="button"
              variant="outline"
              onClick={handleHealthCheck}
              disabled={isChecking}
            >
              {isChecking ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Health Check
            </Button>
          </div>
        </form>

        {/* Connection Status */}
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium mb-2">Connection Status</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Current Backend:</span>
              <span className="font-mono text-xs">{config.backendUrl}</span>
            </div>
            <div className="flex justify-between">
              <span>External Backend:</span>
              <span>{config.isExternalBackend ? 'Yes' : 'No'}</span>
            </div>
            {lastHealthCheck && (
              <div className="flex justify-between">
                <span>Last Check:</span>
                <span>{new Date(lastHealthCheck).toLocaleTimeString()}</span>
              </div>
            )}
          </div>
        </div>

        {/* Configuration Info */}
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium mb-2">Configuration Info</h4>
          <div className="text-sm text-muted-foreground space-y-1">
            <p>• Use the current origin URL for local development</p>
            <p>• Set a different URL to connect to an external backend service</p>
            <p>• Health checks run automatically every minute</p>
            <p>• Configuration is saved locally in your browser</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
