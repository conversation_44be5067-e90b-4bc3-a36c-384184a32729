'use client';

import { useState } from 'react';
import { CV } from '@prisma/client';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { TranslatedLabel } from '@/components/ui/translated-label';
import { useTranslation } from '@/lib/i18n/translation-context';
import { FileUpload } from '@/components/cv/file-upload';
import { getFileTypeDisplay } from '@/lib/utils';

interface CertificatesFormProps {
  cv: CV & {
    files: {
      id: string;
      name: string;
      url: string;
      category: string;
    }[];
  };
}

export function CertificatesForm({ cv }: CertificatesFormProps) {
  const [uploadSuccess, setUploadSuccess] = useState(false);
  const [uploadMessage, setUploadMessage] = useState('');
  const [refreshKey, setRefreshKey] = useState(0);
  const { t } = useTranslation();

  // Find certificate files
  const certificateFiles = cv.files.filter(file => file.category === 'certificate');

  const handleUploadComplete = (url: string, fileName: string) => {
    setUploadSuccess(true);
    setUploadMessage(`Certificate "${fileName}" uploaded successfully!`);

    // Refresh the page to show the new certificate
    // In a production app, you would use React Query or SWR to refresh the data
    // without a full page reload
    setTimeout(() => {
      window.location.reload();
    }, 1500);

    // Clear the success message after 3 seconds
    setTimeout(() => {
      setUploadSuccess(false);
      setUploadMessage('');
    }, 3000);
  };

  const handleDeleteCertificate = async (fileId: string) => {
    if (!confirm('Are you sure you want to delete this certificate?')) {
      return;
    }

    try {
      setUploadSuccess(false);
      setUploadMessage('Deleting certificate...');

      const response = await fetch(`/api/cv/${cv.id}/file/${fileId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete certificate');
      }

      setUploadSuccess(true);
      setUploadMessage('Certificate deleted successfully!');

      // Refresh the page to show updated files
      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (error) {
      console.error('Error deleting certificate:', error);
      setUploadSuccess(false);
      setUploadMessage(`Error: ${error instanceof Error ? error.message : 'Failed to delete certificate'}`);
    }
  };

  const openFile = (url: string) => {
    window.open(url, '_blank');
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium mb-4">{t('cv.certificates')}</h3>
        <p className="text-sm text-muted-foreground mb-6">
          Upload certificates, diplomas, or other documents to include with your CV. These can be selected when adding education entries.
        </p>

        <div className="space-y-6">
          <div>
            <TranslatedLabel htmlFor="certificate-upload" translationKey="certificates.upload" className="block mb-2" />
            <FileUpload
              cvId={cv.id}
              category="certificate"
              onUploadComplete={handleUploadComplete}
              acceptedFileTypes={['application/pdf', 'image/jpeg', 'image/png', 'image/jpg']}
            />

            {uploadMessage && (
              <div className={`mt-4 ${uploadSuccess ? 'bg-green-50 border border-green-200 text-green-700' : 'bg-red-50 border border-red-200 text-red-700'} px-4 py-3 rounded`}>
                {uploadMessage}
              </div>
            )}
          </div>

          {certificateFiles.length > 0 && (
            <div>
              <TranslatedLabel htmlFor="certificate-list" translationKey="certificates.yourCertificates" className="block mb-2" />
              <div className="border rounded-lg divide-y">
                {certificateFiles.map((file) => {
                  // Check if this certificate is used in any education entry
                  const educationEntries = Array.isArray(cv.education) ? cv.education : [];
                  const isUsed = educationEntries.some((entry: any) => entry.certificateUrl === file.url);

                  return (
                    <div key={file.id} className="p-4 flex flex-col sm:flex-row sm:items-center justify-between border-b last:border-b-0 gap-3">
                      <div className="flex-1">
                        <div className="flex flex-wrap items-center gap-2">
                          <p className="font-medium break-all">{file.name}</p>
                          {isUsed && (
                            <span className="text-xs bg-blue-100 text-blue-800 rounded px-2 py-0.5">
                              {t('certificates.inUse')}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground mt-1">
                          {getFileTypeDisplay(file.type)}
                        </p>
                      </div>
                      <div className="flex space-x-2 w-full sm:w-auto justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => openFile(file.url)}
                          className="flex-1 sm:flex-none"
                        >
                          {t('buttons.view')}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-500 hover:text-red-700 flex-1 sm:flex-none"
                          onClick={() => handleDeleteCertificate(file.id)}
                          disabled={isUsed}
                          title={isUsed ? "Cannot delete a certificate that is in use" : ""}
                        >
                          {t('buttons.delete')}
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
