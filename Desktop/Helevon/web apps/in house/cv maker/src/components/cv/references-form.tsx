'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { CV } from '@prisma/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { TranslatedLabel } from '@/components/ui/translated-label';
import { useTranslation } from '@/lib/i18n/translation-context';
import { Reference } from '@/types/cv';
import { useCV } from '@/lib/context/cv-context';

const referenceSchema = z.object({
  id: z.string().optional(),
  name: z.string().min(1, { message: 'Name is required' }),
  company: z.string().min(1, { message: 'Company is required' }),
  position: z.string().min(1, { message: 'Position is required' }),
  email: z.string().email({ message: 'Valid email is required' }).optional().or(z.literal('')),
  phone: z.string().optional(),
  relationship: z.string().min(1, { message: 'Relationship is required' }),
});

type ReferenceFormValues = z.infer<typeof referenceSchema>;

interface ReferencesFormProps {
  cv: CV & {
    files: {
      id: string;
      name: string;
      url: string;
      category: string;
    }[];
  };
}

export function ReferencesForm({ cv: propCV }: ReferencesFormProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentReferenceId, setCurrentReferenceId] = useState<string | null>(null);
  const { t } = useTranslation();
  const { cv, refreshCV } = useCV();

  // Parse the references from the CV
  const references = Array.isArray(cv.references) ? (cv.references as Reference[]) : [];

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<ReferenceFormValues>({
    resolver: zodResolver(referenceSchema),
    defaultValues: {
      name: '',
      company: '',
      position: '',
      email: '',
      phone: '',
      relationship: '',
    },
  });

  const onSubmit = async (data: ReferenceFormValues) => {
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      let updatedReferences: Reference[];

      if (isEditing && currentReferenceId) {
        // Update existing reference
        updatedReferences = references.map(reference =>
          reference.id === currentReferenceId ? { ...data, id: currentReferenceId } : reference
        );
      } else {
        // Add new reference with a unique ID
        const newReference: Reference = {
          ...data,
          id: uuidv4(),
        };
        updatedReferences = [...references, newReference];
      }

      const response = await fetch(`/api/cv/${cv.id}/references`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedReferences),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save references');
      }

      setSaveSuccess(true);
      reset();
      setIsEditing(false);
      setCurrentReferenceId(null);

      // Refresh the CV data to update the UI
      await refreshCV();
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to save references');
    } finally {
      setIsSaving(false);
    }
  };

  const handleEdit = (reference: Reference) => {
    setIsEditing(true);
    setCurrentReferenceId(reference.id);

    // Set form values
    setValue('name', reference.name);
    setValue('company', reference.company);
    setValue('position', reference.position);
    setValue('email', reference.email || '');
    setValue('phone', reference.phone || '');
    setValue('relationship', reference.relationship);
  };

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this reference?')) {
      return;
    }

    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      // Filter out the reference to delete
      const updatedReferences = references.filter(reference => reference.id !== id);

      const response = await fetch(`/api/cv/${cv.id}/references`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedReferences),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete reference');
      }

      setSaveSuccess(true);

      // Refresh the CV data to update the UI
      await refreshCV();
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to delete reference');
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    reset();
    setIsEditing(false);
    setCurrentReferenceId(null);
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium mb-4">{t('cv.references')}</h3>

        {references.length === 0 ? (
          <div className="bg-muted p-6 rounded-lg text-center">
            <p className="text-muted-foreground">No references added yet. Add your first one below.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {references.map((reference) => (
              <div key={reference.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h4 className="font-medium">{reference.name}</h4>
                    <p className="text-muted-foreground">{reference.position} at {reference.company}</p>
                    <p className="text-sm">{reference.relationship}</p>
                    {reference.email && (
                      <p className="text-sm">Email: {reference.email}</p>
                    )}
                    {reference.phone && (
                      <p className="text-sm">Phone: {reference.phone}</p>
                    )}
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" onClick={() => handleEdit(reference)}>
                      {t('buttons.edit')}
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => handleDelete(reference.id)}>
                      {t('buttons.delete')}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="border-t pt-6">
        <h3 className="text-lg font-medium mb-4">
          {isEditing ? t('references.editReference') : t('references.addReference')}
        </h3>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" data-form="references">
          <div className="space-y-2">
            <TranslatedLabel htmlFor="name" translationKey="references.name" />
            <Input
              id="name"
              placeholder="e.g., John Doe"
              {...register('name')}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <TranslatedLabel htmlFor="company" translationKey="references.company" />
              <Input
                id="company"
                placeholder="e.g., Siemens AG"
                {...register('company')}
              />
              {errors.company && (
                <p className="text-sm text-red-500">{errors.company.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <TranslatedLabel htmlFor="position" translationKey="references.position" />
              <Input
                id="position"
                placeholder="e.g., Senior Manager"
                {...register('position')}
              />
              {errors.position && (
                <p className="text-sm text-red-500">{errors.position.message}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <TranslatedLabel htmlFor="email" translationKey="references.email" />
              <Input
                id="email"
                type="email"
                placeholder="e.g., <EMAIL>"
                {...register('email')}
              />
              {errors.email && (
                <p className="text-sm text-red-500">{errors.email.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <TranslatedLabel htmlFor="phone" translationKey="references.phone" />
              <Input
                id="phone"
                placeholder="e.g., +49 123 456789"
                {...register('phone')}
              />
              {errors.phone && (
                <p className="text-sm text-red-500">{errors.phone.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="relationship">Relationship</Label>
            <Input
              id="relationship"
              placeholder="e.g., Former Manager, Colleague"
              {...register('relationship')}
            />
            {errors.relationship && (
              <p className="text-sm text-red-500">{errors.relationship.message}</p>
            )}
          </div>

          {saveError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {saveError}
            </div>
          )}

          {saveSuccess && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
              {t('success.saved')}
            </div>
          )}

          <div className="flex space-x-2">
            {isEditing && (
              <Button type="button" variant="outline" onClick={handleCancel}>
                {t('buttons.cancel')}
              </Button>
            )}
            <Button type="submit" disabled={isSaving}>
              {isSaving
                ? t('buttons.saving')
                : isEditing
                ? t('references.editReference')
                : t('references.addReference')}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
