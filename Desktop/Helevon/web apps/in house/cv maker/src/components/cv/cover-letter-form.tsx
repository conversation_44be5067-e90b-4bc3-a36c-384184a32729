'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { CV } from '@prisma/client';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { TranslatedLabel } from '@/components/ui/translated-label';
import { useTranslation } from '@/lib/i18n/translation-context';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { CoverLetter } from '@/types/cv';
import { FileUpload } from '@/components/ui/file-upload';

const coverLetterSchema = z.object({
  // Recipient information
  recipientName: z.string().optional(),
  recipientCompany: z.string().optional(),
  recipientAddress: z.string().optional(),
  recipientCity: z.string().optional(),
  recipientPostalCode: z.string().optional(),
  recipientCountry: z.string().optional(),
  recipientEmail: z.string().optional(),
  recipientPhone: z.string().optional(),
  recipientOtherInfo: z.string().optional(),

  // Cover letter content
  subject: z.string().optional(),
  content: z.string().min(1, { message: 'Cover letter content is required' }),
  date: z.string().optional(),

  // Signature
  signatureType: z.enum(['text', 'image']).default('text'),
});

type CoverLetterFormValues = z.infer<typeof coverLetterSchema>;

interface CoverLetterFormProps {
  cv: CV & {
    files: {
      id: string;
      name: string;
      url: string;
      category: string;
    }[];
  };
}

export function CoverLetterForm({ cv }: CoverLetterFormProps) {
  const [isSaving, setIsSaving] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [signatureType, setSignatureType] = useState<'text' | 'image'>('text');
  const [signatureImageUrl, setSignatureImageUrl] = useState<string | null>(null);
  const { t } = useTranslation();

  // Parse existing cover letter data if it exists - pure function, no state updates
  const parseCoverLetterData = (): CoverLetterFormValues => {
    if (!cv.coverLetter) {
      return {
        recipientName: '',
        recipientCompany: '',
        recipientAddress: '',
        recipientCity: '',
        recipientPostalCode: '',
        recipientCountry: '',
        recipientEmail: '',
        recipientPhone: '',
        recipientOtherInfo: '',
        subject: '',
        content: '',
        date: new Date().toISOString().split('T')[0],
        signatureType: 'text',
      };
    }

    if (typeof cv.coverLetter === 'string') {
      // Try to parse the string as JSON first
      try {
        const parsedCoverLetter = JSON.parse(cv.coverLetter);

        // Check if it's a valid cover letter object
        if (parsedCoverLetter &&
            (parsedCoverLetter.content ||
             parsedCoverLetter.recipientName ||
             parsedCoverLetter.subject)) {

          // It's a JSON string representing a cover letter object
          const coverLetterObj = parsedCoverLetter as CoverLetter;

          // Return form values (no state updates here)
          return {
            recipientName: coverLetterObj.recipientName || '',
            recipientCompany: coverLetterObj.recipientCompany || '',
            recipientAddress: coverLetterObj.recipientAddress || '',
            recipientCity: coverLetterObj.recipientCity || '',
            recipientPostalCode: coverLetterObj.recipientPostalCode || '',
            recipientCountry: coverLetterObj.recipientCountry || '',
            recipientEmail: coverLetterObj.recipientEmail || '',
            recipientPhone: coverLetterObj.recipientPhone || '',
            recipientOtherInfo: coverLetterObj.recipientOtherInfo || '',
            subject: coverLetterObj.subject || '',
            content: coverLetterObj.content || '',
            date: coverLetterObj.date || new Date().toISOString().split('T')[0],
            signatureType: coverLetterObj.signatureType || 'text',
          };
        }
      } catch (e) {
        // Not a valid JSON string, treat as plain text content
      }

      // If parsing failed or it's not a cover letter object, treat as plain text
      return {
        recipientName: '',
        recipientCompany: '',
        recipientAddress: '',
        recipientCity: '',
        recipientPostalCode: '',
        recipientCountry: '',
        recipientEmail: '',
        recipientPhone: '',
        recipientOtherInfo: '',
        subject: '',
        content: cv.coverLetter,
        date: new Date().toISOString().split('T')[0],
        signatureType: 'text',
      };
    }

    // It's already an object (should not happen with the current schema, but keeping for type safety)
    const coverLetterObj = cv.coverLetter as unknown as CoverLetter;

    return {
      recipientName: coverLetterObj.recipientName || '',
      recipientCompany: coverLetterObj.recipientCompany || '',
      recipientAddress: coverLetterObj.recipientAddress || '',
      recipientCity: coverLetterObj.recipientCity || '',
      recipientPostalCode: coverLetterObj.recipientPostalCode || '',
      recipientCountry: coverLetterObj.recipientCountry || '',
      recipientEmail: coverLetterObj.recipientEmail || '',
      recipientPhone: coverLetterObj.recipientPhone || '',
      recipientOtherInfo: coverLetterObj.recipientOtherInfo || '',
      subject: coverLetterObj.subject || '',
      content: coverLetterObj.content || '',
      date: coverLetterObj.date || new Date().toISOString().split('T')[0],
      signatureType: coverLetterObj.signatureType || 'text',
    };
  };

  // Initialize signature state from cover letter data
  useEffect(() => {
    // Try to extract signature info from cover letter if it exists
    if (cv.coverLetter) {
      let coverLetterObj: CoverLetter | null = null;

      // Parse JSON string if needed
      if (typeof cv.coverLetter === 'string') {
        try {
          const parsedCoverLetter = JSON.parse(cv.coverLetter);
          if (parsedCoverLetter &&
              (parsedCoverLetter.content ||
               parsedCoverLetter.recipientName ||
               parsedCoverLetter.subject)) {
            coverLetterObj = parsedCoverLetter as CoverLetter;
          }
        } catch (e) {
          // Not a valid JSON string
        }
      } else {
        // It's already an object
        coverLetterObj = cv.coverLetter as unknown as CoverLetter;
      }

      // Set signature type and URL if available
      if (coverLetterObj && coverLetterObj.signatureType === 'image' && coverLetterObj.signatureImageUrl) {
        setSignatureImageUrl(coverLetterObj.signatureImageUrl);
        setSignatureType('image');
      }
    }
  }, [cv.coverLetter]);

  // Find signature file if exists
  useEffect(() => {
    const signatureFile = cv.files.find(file => file.category === 'signature');
    if (signatureFile) {
      setSignatureImageUrl(signatureFile.url);
      setSignatureType('image');
    }
  }, [cv.files]);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CoverLetterFormValues>({
    resolver: zodResolver(coverLetterSchema),
    defaultValues: parseCoverLetterData(),
  });

  // Watch signature type to update UI
  const watchSignatureType = watch('signatureType');

  // Handle signature type change
  useEffect(() => {
    setSignatureType(watchSignatureType);
  }, [watchSignatureType]);

  // Handle signature file upload
  const handleSignatureUpload = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('category', 'signature');

    try {
      const response = await fetch(`/api/cv/${cv.id}/upload`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('Failed to upload signature');
      }

      const data = await response.json();
      setSignatureImageUrl(data.url);
      setValue('signatureType', 'image');
    } catch (error) {
      console.error('Error uploading signature:', error);
      setSaveError(error instanceof Error ? error.message : 'Failed to upload signature');
    }
  };

  const onSubmit = async (data: CoverLetterFormValues) => {
    setIsSaving(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      // Create the cover letter object
      const coverLetterData: CoverLetter = {
        ...data,
        signatureImageUrl: signatureType === 'image' ? signatureImageUrl : undefined,
      };

      const response = await fetch(`/api/cv/${cv.id}/cover-letter`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          coverLetter: coverLetterData,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to save cover letter');
      }

      setSaveSuccess(true);
    } catch (error) {
      setSaveError(error instanceof Error ? error.message : 'Failed to save cover letter');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-lg font-medium mb-4">{t('cv.coverLetter')}</h3>
        <p className="text-sm text-muted-foreground mb-6">
          Create a professional German-style cover letter with proper formatting. The subject will be used as the title on the cover page.
        </p>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6" data-form="cover-letter">
          <Tabs defaultValue="recipient" className="w-full">
            <TabsList className="grid grid-cols-3 w-full">
              <TabsTrigger value="recipient">{t('coverLetter.recipient')}</TabsTrigger>
              <TabsTrigger value="content">{t('coverLetter.content')}</TabsTrigger>
              <TabsTrigger value="signature">{t('coverLetter.signature')}</TabsTrigger>
            </TabsList>

            {/* Recipient Information Tab */}
            <TabsContent value="recipient" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <TranslatedLabel htmlFor="recipientName" translationKey="coverLetter.recipientName" />
                  <Input
                    id="recipientName"
                    placeholder="e.g., Herr Schmidt or Frau Müller"
                    {...register('recipientName')}
                  />
                  <p className="text-xs text-muted-foreground">
                    Include title (Herr/Frau) for proper salutation
                  </p>
                </div>

                <div className="space-y-2">
                  <TranslatedLabel htmlFor="recipientCompany" translationKey="coverLetter.company" />
                  <Input
                    id="recipientCompany"
                    placeholder="e.g., Musterfirma GmbH"
                    {...register('recipientCompany')}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <TranslatedLabel htmlFor="recipientAddress" translationKey="coverLetter.address" />
                <Input
                  id="recipientAddress"
                  placeholder="e.g., Musterstraße 123"
                  {...register('recipientAddress')}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <TranslatedLabel htmlFor="recipientPostalCode" translationKey="coverLetter.postalCode" />
                  <Input
                    id="recipientPostalCode"
                    placeholder="e.g., 12345"
                    {...register('recipientPostalCode')}
                  />
                </div>

                <div className="space-y-2">
                  <TranslatedLabel htmlFor="recipientCity" translationKey="coverLetter.city" />
                  <Input
                    id="recipientCity"
                    placeholder="e.g., Berlin"
                    {...register('recipientCity')}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <TranslatedLabel htmlFor="recipientCountry" translationKey="coverLetter.country" />
                <Input
                  id="recipientCountry"
                  placeholder="e.g., Deutschland"
                  defaultValue="Deutschland"
                  {...register('recipientCountry')}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <TranslatedLabel htmlFor="recipientEmail" translationKey="coverLetter.email" />
                  <Input
                    id="recipientEmail"
                    type="email"
                    placeholder="e.g., <EMAIL>"
                    {...register('recipientEmail')}
                  />
                </div>

                <div className="space-y-2">
                  <TranslatedLabel htmlFor="recipientPhone" translationKey="coverLetter.phone" />
                  <Input
                    id="recipientPhone"
                    placeholder="e.g., +49 123 456789"
                    {...register('recipientPhone')}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <TranslatedLabel htmlFor="recipientOtherInfo" translationKey="coverLetter.otherInfo" />
                <Input
                  id="recipientOtherInfo"
                  placeholder="Any additional recipient information"
                  {...register('recipientOtherInfo')}
                />
              </div>
            </TabsContent>

            {/* Cover Letter Content Tab */}
            <TabsContent value="content" className="space-y-4 pt-4">
              <div className="space-y-2">
                <TranslatedLabel htmlFor="subject" translationKey="coverLetter.subject" />
                <Input
                  id="subject"
                  placeholder="e.g., Bewerbung um eine Ausbildung zum Fachinformatiker"
                  {...register('subject')}
                />
                <p className="text-xs text-muted-foreground">
                  This will appear as the title on your cover page
                </p>
              </div>

              <div className="space-y-2">
                <TranslatedLabel htmlFor="date" translationKey="coverLetter.date" />
                <Input
                  id="date"
                  type="date"
                  {...register('date')}
                />
              </div>

              <div className="space-y-2">
                <TranslatedLabel htmlFor="content" translationKey="coverLetter.letterContent" />
                <textarea
                  id="content"
                  className="flex min-h-[300px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  placeholder="Write your cover letter content here..."
                  {...register('content')}
                />
                {errors.content && (
                  <p className="text-sm text-red-500">{errors.content.message}</p>
                )}
                <p className="text-xs text-muted-foreground">
                  The salutation will be automatically generated based on the recipient name (if provided)
                </p>
              </div>
            </TabsContent>

            {/* Signature Tab */}
            <TabsContent value="signature" className="space-y-4 pt-4">
              <div className="space-y-2">
                <TranslatedLabel htmlFor="signatureType" translationKey="coverLetter.signatureType" />
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div
                    className={`border rounded-lg p-4 cursor-pointer hover:border-primary transition-colors ${signatureType === 'text' ? 'border-primary bg-primary/5' : ''}`}
                    onClick={() => {
                      setValue('signatureType', 'text');
                      setSignatureType('text');
                    }}
                  >
                    <h4 className="font-medium">{t('coverLetter.textSignature')}</h4>
                    <p className="text-sm text-muted-foreground">
                      Use your name as the signature
                    </p>
                  </div>
                  <div
                    className={`border rounded-lg p-4 cursor-pointer hover:border-primary transition-colors ${signatureType === 'image' ? 'border-primary bg-primary/5' : ''}`}
                    onClick={() => {
                      setValue('signatureType', 'image');
                      setSignatureType('image');
                    }}
                  >
                    <h4 className="font-medium">{t('coverLetter.uploadSignature')}</h4>
                    <p className="text-sm text-muted-foreground">
                      Upload an image of your handwritten signature
                    </p>
                  </div>
                </div>
              </div>

              {signatureType === 'image' && (
                <div className="space-y-2">
                  <TranslatedLabel htmlFor="signature-upload" translationKey="coverLetter.uploadSignature" />
                  <div className="border rounded-lg p-6 flex flex-col items-center justify-center">
                    {signatureImageUrl ? (
                      <div className="space-y-4">
                        <div className="flex justify-center">
                          <img
                            src={signatureImageUrl}
                            alt="Signature"
                            className="max-h-24 border p-2"
                          />
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => {
                            setSignatureImageUrl(null);
                            setValue('signatureType', 'text');
                          }}
                        >
                          {t('coverLetter.removeSignature')}
                        </Button>
                      </div>
                    ) : (
                      <FileUpload
                        endpoint={`/api/cv/${cv.id}/upload`}
                        onUploadComplete={(data) => {
                          setSignatureImageUrl(data.url);
                        }}
                        fileCategory="signature"
                        acceptedFileTypes={{
                          'image/jpeg': ['.jpg', '.jpeg'],
                          'image/png': ['.png'],
                        }}
                      />
                    )}
                    <p className="text-xs text-muted-foreground mt-2">
                      Upload a clear image of your signature (PNG or JPG)
                    </p>
                  </div>
                </div>
              )}
            </TabsContent>
          </Tabs>

          {saveError && (
            <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
              {saveError}
            </div>
          )}

          {saveSuccess && (
            <div className="bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded">
              {t('success.saved')}
            </div>
          )}

          <Button type="submit" disabled={isSaving}>
            {isSaving ? t('buttons.saving') : t('buttons.save')}
          </Button>
        </form>
      </div>
    </div>
  );
}
