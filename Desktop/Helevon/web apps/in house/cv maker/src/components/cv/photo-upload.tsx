'use client';

import { useState, useCallback } from 'react';
import Image from 'next/image';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { validateFileSize, validateFileType } from '@/lib/utils';

interface PhotoUploadProps {
  cvId: string;
  initialPhotoUrl?: string;
  onUploadComplete: (url: string) => void;
}

export function PhotoUpload({
  cvId,
  initialPhotoUrl,
  onUploadComplete,
}: PhotoUploadProps) {
  const [photoUrl, setPhotoUrl] = useState<string | undefined>(initialPhotoUrl);
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      
      if (!file) return;

      // Validate file size (max 5MB)
      if (!validateFileSize(file.size, 5)) {
        setError('File is too large. Maximum size is 5MB.');
        return;
      }

      // Validate file type
      if (!validateFileType(file.type, ['image/jpeg', 'image/png', 'image/jpg'])) {
        setError('Invalid file type. Only JPEG and PNG images are allowed.');
        return;
      }

      setIsUploading(true);
      setError(null);

      try {
        // Create form data
        const formData = new FormData();
        formData.append('file', file);
        formData.append('category', 'photo');

        // Upload the file
        const response = await fetch(`/api/cv/${cvId}/upload`, {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to upload photo');
        }

        const data = await response.json();
        setPhotoUrl(data.url);
        onUploadComplete(data.url);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to upload photo');
      } finally {
        setIsUploading(false);
      }
    },
    [cvId, onUploadComplete]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/jpeg': [],
      'image/png': [],
    },
    maxFiles: 1,
  });

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Profile Photo</h3>
      <p className="text-sm text-muted-foreground">
        Upload a professional photo for your CV. A head-and-shoulders shot with a neutral background is recommended.
      </p>

      {photoUrl ? (
        <div className="space-y-4">
          <div className="relative w-40 h-40 mx-auto overflow-hidden rounded-full border">
            <Image
              src={photoUrl}
              alt="Profile photo"
              fill
              style={{ objectFit: 'cover' }}
            />
          </div>
          <div className="flex justify-center">
            <Button
              variant="outline"
              onClick={() => {
                setPhotoUrl(undefined);
                onUploadComplete('');
              }}
            >
              Remove Photo
            </Button>
          </div>
        </div>
      ) : (
        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
            isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/20'
          }`}
        >
          <input {...getInputProps()} />
          {isUploading ? (
            <div className="text-center">
              <p className="text-sm text-muted-foreground">Uploading...</p>
            </div>
          ) : (
            <div className="text-center">
              <p className="text-sm font-medium">
                {isDragActive
                  ? 'Drop the photo here'
                  : 'Drag and drop your photo here, or click to select'}
              </p>
              <p className="text-xs text-muted-foreground mt-1">
                JPEG or PNG, max 5MB
              </p>
            </div>
          )}
        </div>
      )}

      {error && (
        <p className="text-sm text-red-500 text-center">{error}</p>
      )}
    </div>
  );
}
