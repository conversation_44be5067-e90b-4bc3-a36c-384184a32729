import { Document, Page, Text, View, StyleSheet, Image, Font } from '@react-pdf/renderer';
import { CV } from '@prisma/client';
import { PersonalInfo, EducationEntry, WorkExperienceEntry, Skill, Reference } from '@/types/cv';
import { formatDate } from '@/lib/utils';

// Register fonts
Font.register({
  family: 'Roboto',
  fonts: [
    { src: 'https://cdn.jsdelivr.net/npm/@canvas-fonts/roboto-regular@1.0.4/Roboto-Regular.ttf', fontWeight: 'normal' },
    { src: 'https://cdn.jsdelivr.net/npm/@canvas-fonts/roboto-bold@1.0.4/Roboto-Bold.ttf', fontWeight: 'bold' },
  ],
});

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Roboto',
  },
  header: {
    flexDirection: 'row',
    marginBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#EEEEEE',
    paddingBottom: 20,
  },
  photoContainer: {
    width: 80,
    height: 80,
    marginRight: 20,
  },
  photo: {
    width: '100%',
    height: '100%',
    borderRadius: 40,
  },
  headerContent: {
    flex: 1,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  contactInfo: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    fontSize: 10,
  },
  contactItem: {
    marginRight: 15,
    marginBottom: 5,
  },
  contactLabel: {
    fontWeight: 'bold',
    marginRight: 5,
  },
  section: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  educationContainer: {
    marginBottom: 10,
  },
  educationItem: {
    marginBottom: 10,
  },
  educationHeader: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  educationDate: {
    width: '30%',
    fontSize: 11,
    color: '#666666',
  },
  educationContent: {
    width: '70%',
  },
  educationTitle: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  educationInstitution: {
    fontSize: 11,
  },
  educationDescription: {
    fontSize: 10,
    color: '#666666',
    marginTop: 3,
  },
  workContainer: {
    marginBottom: 10,
  },
  workItem: {
    marginBottom: 10,
  },
  workHeader: {
    flexDirection: 'row',
    marginBottom: 5,
  },
  workDate: {
    width: '30%',
    fontSize: 11,
    color: '#666666',
  },
  workContent: {
    width: '70%',
  },
  workTitle: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  workCompany: {
    fontSize: 11,
  },
  workDescription: {
    fontSize: 10,
    color: '#666666',
    marginTop: 3,
  },
  skillsContainer: {
    marginBottom: 10,
  },
  skillsCategory: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 5,
    marginTop: 10,
  },
  skillsRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  skillItem: {
    marginRight: 15,
    marginBottom: 5,
  },
  skillName: {
    fontSize: 11,
  },
  skillLevel: {
    fontSize: 10,
    color: '#666666',
  },
  referencesContainer: {
    marginBottom: 10,
  },
  referenceItem: {
    marginBottom: 10,
  },
  referenceName: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  referencePosition: {
    fontSize: 11,
  },
  referenceContact: {
    fontSize: 10,
    color: '#666666',
  },
  coverLetter: {
    fontSize: 11,
    lineHeight: 1.5,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    fontSize: 10,
    color: '#666666',
    textAlign: 'center',
    borderTopWidth: 1,
    borderTopColor: '#EEEEEE',
    paddingTop: 5,
  },
});

interface StandardTemplateProps {
  cv: CV;
  personalInfo: PersonalInfo;
  education: EducationEntry[];
  workExperience: WorkExperienceEntry[];
  skills: Skill[];
  references: Reference[];
  photoUrl?: string;
}

export function StandardTemplate({
  cv,
  personalInfo,
  education,
  workExperience,
  skills,
  references,
  photoUrl,
}: StandardTemplateProps) {
  // Group skills by category
  const technicalSkills = skills.filter(skill => skill.category === 'technical');
  const languageSkills = skills.filter(skill => skill.category === 'language');
  const softSkills = skills.filter(skill => skill.category === 'soft');

  // Sort entries by date (most recent first)
  const sortedEducation = [...education].sort((a, b) => {
    const dateA = a.current ? new Date() : new Date(a.endDate || a.startDate);
    const dateB = b.current ? new Date() : new Date(b.endDate || b.startDate);
    return dateB.getTime() - dateA.getTime();
  });

  const sortedWorkExperience = [...workExperience].sort((a, b) => {
    const dateA = a.current ? new Date() : new Date(a.endDate || a.startDate);
    const dateB = b.current ? new Date() : new Date(b.endDate || b.startDate);
    return dateB.getTime() - dateA.getTime();
  });

  // Log the photo URL for debugging
  if (photoUrl) {
    console.log(`StandardTemplate: Using photo URL: ${photoUrl}`);
  } else {
    console.log('StandardTemplate: No photo URL provided');
  }

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header with Personal Info */}
        <View style={styles.header}>
          {photoUrl && (
            <View style={styles.photoContainer}>
              <Image
                src={
                  photoUrl.startsWith('http')
                    ? photoUrl
                    : photoUrl.startsWith('/api/')
                      ? `http://localhost:3000${photoUrl}`
                      : photoUrl.startsWith('/uploads/')
                        ? `http://localhost:3000/api/files/by-path?path=${encodeURIComponent(photoUrl)}`
                        : `http://localhost:3000${photoUrl}`
                }
                style={styles.photo}
                cache={false}
              />
            </View>
          )}
          <View style={styles.headerContent}>
            <Text style={styles.name}>{personalInfo.firstName} {personalInfo.lastName}</Text>
            <View style={styles.contactInfo}>
              {personalInfo.email && (
                <View style={styles.contactItem}>
                  <Text>
                    <Text style={styles.contactLabel}>Email:</Text> {personalInfo.email}
                  </Text>
                </View>
              )}
              {personalInfo.phone && (
                <View style={styles.contactItem}>
                  <Text>
                    <Text style={styles.contactLabel}>Phone:</Text> {personalInfo.phone}
                  </Text>
                </View>
              )}
              {personalInfo.address && (
                <View style={styles.contactItem}>
                  <Text>
                    <Text style={styles.contactLabel}>Address:</Text> {personalInfo.address}, {personalInfo.postalCode} {personalInfo.city}, {personalInfo.country}
                  </Text>
                </View>
              )}
              {personalInfo.dateOfBirth && (
                <View style={styles.contactItem}>
                  <Text>
                    <Text style={styles.contactLabel}>Date of Birth:</Text> {personalInfo.dateOfBirth}
                  </Text>
                </View>
              )}
              {personalInfo.nationality && (
                <View style={styles.contactItem}>
                  <Text>
                    <Text style={styles.contactLabel}>Nationality:</Text> {personalInfo.nationality}
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>

        {/* Education */}
        {education.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Education</Text>
            <View style={styles.educationContainer}>
              {sortedEducation.map((edu, index) => (
                <View key={index} style={styles.educationItem}>
                  <View style={styles.educationHeader}>
                    <Text style={styles.educationDate}>
                      {formatDate(edu.startDate)} - {edu.current ? 'Present' : edu.endDate ? formatDate(edu.endDate) : ''}
                    </Text>
                    <View style={styles.educationContent}>
                      <Text style={styles.educationTitle}>{edu.degree} in {edu.field}</Text>
                      <Text style={styles.educationInstitution}>{edu.institution}, {edu.location}</Text>
                      {edu.description && (
                        <Text style={styles.educationDescription}>{edu.description}</Text>
                      )}
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Work Experience */}
        {workExperience.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Work Experience</Text>
            <View style={styles.workContainer}>
              {sortedWorkExperience.map((work, index) => (
                <View key={index} style={styles.workItem}>
                  <View style={styles.workHeader}>
                    <Text style={styles.workDate}>
                      {formatDate(work.startDate)} - {work.current ? 'Present' : work.endDate ? formatDate(work.endDate) : ''}
                    </Text>
                    <View style={styles.workContent}>
                      <Text style={styles.workTitle}>{work.position}</Text>
                      <Text style={styles.workCompany}>{work.company}, {work.location}</Text>
                      {work.description && (
                        <Text style={styles.workDescription}>{work.description}</Text>
                      )}
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Skills */}
        {skills.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Skills</Text>
            <View style={styles.skillsContainer}>
              {technicalSkills.length > 0 && (
                <>
                  <Text style={styles.skillsCategory}>Technical Skills</Text>
                  <View style={styles.skillsRow}>
                    {technicalSkills.map((skill, index) => (
                      <View key={index} style={styles.skillItem}>
                        <Text style={styles.skillName}>{skill.name}</Text>
                        <Text style={styles.skillLevel}>Level: {skill.level}/5</Text>
                      </View>
                    ))}
                  </View>
                </>
              )}

              {languageSkills.length > 0 && (
                <>
                  <Text style={styles.skillsCategory}>Language Skills</Text>
                  <View style={styles.skillsRow}>
                    {languageSkills.map((skill, index) => (
                      <View key={index} style={styles.skillItem}>
                        <Text style={styles.skillName}>{skill.name}</Text>
                        <Text style={styles.skillLevel}>Level: {skill.level}/5</Text>
                      </View>
                    ))}
                  </View>
                </>
              )}

              {softSkills.length > 0 && (
                <>
                  <Text style={styles.skillsCategory}>Soft Skills</Text>
                  <View style={styles.skillsRow}>
                    {softSkills.map((skill, index) => (
                      <View key={index} style={styles.skillItem}>
                        <Text style={styles.skillName}>{skill.name}</Text>
                        <Text style={styles.skillLevel}>Level: {skill.level}/5</Text>
                      </View>
                    ))}
                  </View>
                </>
              )}
            </View>
          </View>
        )}

        {/* References */}
        {references.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>References</Text>
            <View style={styles.referencesContainer}>
              {references.map((reference, index) => (
                <View key={index} style={styles.referenceItem}>
                  <Text style={styles.referenceName}>{reference.name}</Text>
                  <Text style={styles.referencePosition}>{reference.position} at {reference.company}</Text>
                  <Text style={styles.referencePosition}>{reference.relationship}</Text>
                  {reference.email && (
                    <Text style={styles.referenceContact}>Email: {reference.email}</Text>
                  )}
                  {reference.phone && (
                    <Text style={styles.referenceContact}>Phone: {reference.phone}</Text>
                  )}
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Cover Letter */}
        {cv.coverLetter && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Cover Letter</Text>
            <Text style={styles.coverLetter}>{cv.coverLetter}</Text>
          </View>
        )}

        {/* Footer */}
        <View style={styles.footer}>
          <Text>Generated with CV Maker | {new Date().toLocaleDateString()}</Text>
        </View>
      </Page>
    </Document>
  );
}
