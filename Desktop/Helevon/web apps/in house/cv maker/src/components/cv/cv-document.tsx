import { CV } from '@prisma/client';
import { PersonalInfo, EducationEntry, WorkExperienceEntry, Skill, Reference, CoverLetter } from '@/types/cv';
import { GermanAusbildungTemplate } from './templates/german-ausbildung-template';
import { StandardTemplate } from './templates/standard-template';

interface CVDocumentProps {
  cv: CV;
  personalInfo: PersonalInfo;
  education: EducationEntry[];
  workExperience: WorkExperienceEntry[];
  skills: Skill[];
  references: Reference[];
  photoUrl?: string;
  targetPosition?: string;
  field?: string;
}

export function CVDocument({
  cv,
  personalInfo,
  education,
  workExperience,
  skills,
  references,
  photoUrl,
  targetPosition,
  field,
}: CVDocumentProps) {
  // Choose template based on CV template setting
  if (cv.template === 'german-ausbildung' || cv.language === 'de') {
    return (
      <GermanAusbildungTemplate
        cv={cv}
        personalInfo={personalInfo}
        education={education}
        workExperience={workExperience}
        skills={skills}
        references={references}
        photoUrl={photoUrl}
        targetPosition={targetPosition || "Fachinformatiker"}
        field={field || "Anwendungsentwicklung"}
      />
    );
  }

  // Default to standard template
  return (
    <StandardTemplate
      cv={cv}
      personalInfo={personalInfo}
      education={education}
      workExperience={workExperience}
      skills={skills}
      references={references}
      photoUrl={photoUrl}
    />
  );
}
