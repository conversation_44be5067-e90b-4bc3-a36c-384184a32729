'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from '@/components/ui/button';
import { validateFileSize, validateFileType } from '@/lib/utils';

interface FileUploadProps {
  cvId: string;
  category: 'certificate' | 'cover_letter' | 'other';
  onUploadComplete: (url: string, fileName: string) => void;
  acceptedFileTypes?: string[];
  maxSizeMB?: number;
}

export function FileUpload({
  cvId,
  category,
  onUploadComplete,
  acceptedFileTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'],
  maxSizeMB = 5,
}: FileUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      const file = acceptedFiles[0];
      
      if (!file) return;

      // Validate file size
      if (!validateFileSize(file.size, maxSizeMB)) {
        setError(`File is too large. Maximum size is ${maxSizeMB}MB.`);
        return;
      }

      // Validate file type
      if (!validateFileType(file.type, acceptedFileTypes)) {
        setError(`Invalid file type. Accepted types: ${acceptedFileTypes.join(', ')}`);
        return;
      }

      setIsUploading(true);
      setError(null);

      try {
        // Create form data
        const formData = new FormData();
        formData.append('file', file);
        formData.append('category', category);

        // Upload the file
        const response = await fetch(`/api/cv/${cvId}/upload`, {
          method: 'POST',
          body: formData,
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || 'Failed to upload file');
        }

        const data = await response.json();
        onUploadComplete(data.url, file.name);
      } catch (error) {
        setError(error instanceof Error ? error.message : 'Failed to upload file');
      } finally {
        setIsUploading(false);
      }
    },
    [cvId, category, onUploadComplete, acceptedFileTypes, maxSizeMB]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes.reduce((acc, type) => {
      acc[type] = [];
      return acc;
    }, {} as Record<string, string[]>),
    maxFiles: 1,
  });

  const getFileTypeDescription = () => {
    if (acceptedFileTypes.includes('application/pdf') && 
        acceptedFileTypes.some(type => type.startsWith('image/'))) {
      return 'PDF or Image';
    } else if (acceptedFileTypes.includes('application/pdf')) {
      return 'PDF';
    } else if (acceptedFileTypes.some(type => type.startsWith('image/'))) {
      return 'Image (JPEG, PNG)';
    } else {
      return acceptedFileTypes.join(', ');
    }
  };

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
          isDragActive ? 'border-primary bg-primary/5' : 'border-muted-foreground/20'
        }`}
      >
        <input {...getInputProps()} />
        {isUploading ? (
          <div className="text-center">
            <p className="text-sm text-muted-foreground">Uploading...</p>
          </div>
        ) : (
          <div className="text-center">
            <p className="text-sm font-medium">
              {isDragActive
                ? 'Drop the file here'
                : 'Drag and drop your file here, or click to select'}
            </p>
            <p className="text-xs text-muted-foreground mt-1">
              {getFileTypeDescription()}, max {maxSizeMB}MB
            </p>
          </div>
        )}
      </div>

      {error && (
        <p className="text-sm text-red-500 text-center">{error}</p>
      )}
    </div>
  );
}
