import { Metadata } from 'next';
import { notFound, redirect } from 'next/navigation';
import { auth } from '@/lib/auth/auth';
import { prisma } from '@/lib/db/prisma';
import { CVPreview } from '@/components/cv/cv-preview';

export const metadata: Metadata = {
  title: 'Preview CV - CV Maker',
  description: 'Preview your CV',
};

interface CVPreviewPageProps {
  params: {
    id: string;
  };
}

export default async function CVPreviewPage({ params }: CVPreviewPageProps) {
  const session = await auth();
  // Await params to avoid Next.js warning
  const { id } = await Promise.resolve(params);

  if (!session?.user) {
    redirect('/login');
  }

  const cv = await prisma.cV.findUnique({
    where: {
      id,
    },
    include: {
      files: true,
    },
  });

  if (!cv) {
    notFound();
  }

  // Check if the CV belongs to the current user
  if (cv.userId !== session.user.id) {
    redirect('/dashboard');
  }

  return (
    <div className="container py-10">
      <CVPreview cv={cv} />
    </div>
  );
}
