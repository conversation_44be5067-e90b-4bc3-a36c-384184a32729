import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { auth } from '@/lib/auth/auth';
import { NewCVForm } from './new-cv-form';
import { NewCVTitle } from './new-cv-title';

export const metadata: Metadata = {
  title: 'Create New CV - CV Maker',
  description: 'Create a new CV for your job applications',
};

export default async function NewCVPage() {
  const session = await auth();

  if (!session?.user) {
    redirect('/login');
  }

  return (
    <div className="container py-10">
      <div className="max-w-3xl mx-auto">
        <NewCVTitle />
        <NewCVForm userId={session.user.id} />
      </div>
    </div>
  );
}
