import { Metadata } from 'next';
import { notFound, redirect } from 'next/navigation';
import { auth } from '@/lib/auth/auth';
import { prisma } from '@/lib/db/prisma';
import { CVEditor } from '@/components/cv/cv-editor';

export const metadata: Metadata = {
  title: 'Edit CV - CV Maker',
  description: 'Edit your CV',
};

interface CVEditPageProps {
  params: {
    id: string;
  };
}

export default async function CVEditPage({ params }: CVEditPageProps) {
  const session = await auth();

  if (!session?.user) {
    redirect('/login');
  }

  // Await params to avoid Next.js warning
  const { id } = await Promise.resolve(params);

  const cv = await prisma.cV.findUnique({
    where: {
      id,
    },
    include: {
      files: true,
    },
  });

  if (!cv) {
    notFound();
  }

  // Check if the CV belongs to the current user
  if (cv.userId !== session.user.id) {
    redirect('/dashboard');
  }

  return (
    <div className="container py-10">
      <CVEditor cv={cv} />
    </div>
  );
}
