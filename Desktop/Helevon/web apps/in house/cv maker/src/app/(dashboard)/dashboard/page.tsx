import { Metadata } from 'next';
import Link from 'next/link';
import { redirect } from 'next/navigation';
import { auth } from '@/lib/auth/auth';
import { Button } from '@/components/ui/button';
import { prisma } from '@/lib/db/prisma';

export const metadata: Metadata = {
  title: 'Dashboard - CV Maker',
  description: 'Manage your CVs and applications',
};

export default async function DashboardPage() {
  const session = await auth();

  if (!session?.user) {
    redirect('/login');
  }

  const cvs = await prisma.cV.findMany({
    where: {
      userId: session.user.id,
    },
    orderBy: {
      updatedAt: 'desc',
    },
  });

  return (
    <div className="container py-10">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Dashboard</h1>
        <Button asChild>
          <Link href="/cv/new">Create New CV</Link>
        </Button>
      </div>

      <div className="grid gap-6">
        <div>
          <h2 className="text-xl font-semibold mb-4">Your CVs</h2>
          {cvs.length === 0 ? (
            <div className="bg-muted p-8 rounded-lg text-center">
              <h3 className="text-lg font-medium mb-2">No CVs yet</h3>
              <p className="text-muted-foreground mb-4">
                Create your first CV to get started with your job applications.
              </p>
              <Button asChild>
                <Link href="/cv/new">Create Your First CV</Link>
              </Button>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {cvs.map((cv) => (
                <div
                  key={cv.id}
                  className="border rounded-lg overflow-hidden shadow-sm"
                >
                  <div className="p-6">
                    <h3 className="text-lg font-medium mb-2">{cv.title}</h3>
                    <p className="text-sm text-muted-foreground mb-4">
                      Last updated:{' '}
                      {new Date(cv.updatedAt).toLocaleDateString()}
                    </p>
                    <div className="flex gap-2">
                      <Button asChild size="sm" variant="outline">
                        <Link href={`/cv/${cv.id}/edit`}>Edit</Link>
                      </Button>
                      <Button asChild size="sm">
                        <Link href={`/cv/${cv.id}/preview`}>Preview</Link>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
