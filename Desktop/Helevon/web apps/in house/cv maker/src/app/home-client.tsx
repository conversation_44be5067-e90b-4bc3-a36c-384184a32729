'use client';

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { useTranslation } from "@/lib/i18n/translation-context";

export function HomeClient() {
  const { t } = useTranslation();
  
  return (
    <div className="flex flex-col items-center justify-center min-h-[calc(100vh-8rem)] p-8">
      <div className="max-w-5xl mx-auto text-center">
        <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl">
          {t('home.title')}
        </h1>
        <p className="mt-6 text-lg text-muted-foreground max-w-3xl mx-auto">
          {t('home.description')}
        </p>
        <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg" className="px-8">
            <Link href="/register">{t('home.getStarted')}</Link>
          </Button>
          <Button asChild variant="outline" size="lg" className="px-8">
            <Link href="/login">{t('home.signIn')}</Link>
          </Button>
        </div>
      </div>

      <div className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        <div className="flex flex-col items-center text-center p-6 rounded-lg border bg-card">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-primary"
            >
              <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
              <polyline points="14 2 14 8 20 8" />
            </svg>
          </div>
          <h3 className="text-xl font-bold">{t('home.feature1.title')}</h3>
          <p className="mt-2 text-muted-foreground">
            {t('home.feature1.description')}
          </p>
        </div>

        <div className="flex flex-col items-center text-center p-6 rounded-lg border bg-card">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-primary"
            >
              <circle cx="12" cy="12" r="10" />
              <path d="m16 12-4 4-4-4" />
              <path d="M12 8v8" />
            </svg>
          </div>
          <h3 className="text-xl font-bold">{t('home.feature2.title')}</h3>
          <p className="mt-2 text-muted-foreground">
            {t('home.feature2.description')}
          </p>
        </div>

        <div className="flex flex-col items-center text-center p-6 rounded-lg border bg-card">
          <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-primary"
            >
              <path d="M2 12h5" />
              <path d="M17 12h5" />
              <path d="M12 2v5" />
              <path d="M12 17v5" />
              <path d="M4.93 4.93l3.54 3.54" />
              <path d="M15.54 15.54l3.54 3.54" />
              <path d="M15.54 4.93l-3.54 3.54" />
              <path d="M4.93 15.54l3.54-3.54" />
            </svg>
          </div>
          <h3 className="text-xl font-bold">{t('home.feature3.title')}</h3>
          <p className="mt-2 text-muted-foreground">
            {t('home.feature3.description')}
          </p>
        </div>
      </div>
    </div>
  );
}
