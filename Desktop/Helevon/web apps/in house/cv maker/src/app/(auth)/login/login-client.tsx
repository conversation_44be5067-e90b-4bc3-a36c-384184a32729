'use client';

import Link from 'next/link';
import { LoginForm } from './login-form';
import { useTranslation } from '@/lib/i18n/translation-context';

export function LoginClient() {
  const { t } = useTranslation();
  
  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">
            {t('auth.welcomeBack')}
          </h1>
          <p className="text-sm text-muted-foreground">
            {t('auth.enterEmail')}
          </p>
        </div>
        <LoginForm />
        <p className="px-8 text-center text-sm text-muted-foreground">
          <Link
            href="/register"
            className="hover:text-brand underline underline-offset-4"
          >
            {t('auth.noAccount')} {t('auth.signUp')}
          </Link>
        </p>
      </div>
    </div>
  );
}
