'use client';

import Link from 'next/link';
import { RegisterForm } from './register-form';
import { useTranslation } from '@/lib/i18n/translation-context';

export function RegisterClient() {
  const { t } = useTranslation();
  
  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <h1 className="text-2xl font-semibold tracking-tight">
            {t('auth.createAccount')}
          </h1>
          <p className="text-sm text-muted-foreground">
            {t('auth.enterDetails')}
          </p>
        </div>
        <RegisterForm />
        <p className="px-8 text-center text-sm text-muted-foreground">
          <Link
            href="/login"
            className="hover:text-brand underline underline-offset-4"
          >
            {t('auth.hasAccount')} {t('auth.signIn')}
          </Link>
        </p>
      </div>
    </div>
  );
}
