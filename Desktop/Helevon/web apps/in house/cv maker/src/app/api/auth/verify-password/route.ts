import { NextRequest, NextResponse } from 'next/server';
import { compare } from 'bcrypt';
import { z } from 'zod';

const verifyPasswordSchema = z.object({
  password: z.string().min(1),
  hashedPassword: z.string().min(1),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { password, hashedPassword } = verifyPasswordSchema.parse(body);

    // Verify the password
    const isValid = await compare(password, hashedPassword);
    
    return NextResponse.json({ isValid });
  } catch (error) {
    console.error('Password verification error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid input data', errors: error.errors, isValid: false },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal server error', isValid: false },
      { status: 500 }
    );
  }
}
