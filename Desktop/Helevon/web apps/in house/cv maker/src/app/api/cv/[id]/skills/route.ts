import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/db/prisma';
import { auth } from '@/lib/auth/auth';

const skillSchema = z.object({
  id: z.string(),
  name: z.string().min(1, { message: 'Skill name is required' }),
  level: z.number().min(1).max(5),
  category: z.enum(['technical', 'language', 'soft']),
});

const skillsArraySchema = z.array(skillSchema);

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await Promise.resolve(params);

    // Check if the CV exists and belongs to the user
    const cv = await prisma.cV.findUnique({
      where: {
        id,
      },
    });

    if (!cv) {
      return NextResponse.json(
        { message: 'CV not found' },
        { status: 404 }
      );
    }

    if (cv.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const body = await req.json();
    const skills = skillsArraySchema.parse(body);

    // Update the CV with the new skills
    const updatedCV = await prisma.cV.update({
      where: {
        id,
      },
      data: {
        skills: skills,
      },
    });

    return NextResponse.json(updatedCV);
  } catch (error) {
    console.error('Skills update error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid input data', errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
