import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/db/prisma';
import { auth } from '@/lib/auth/auth';

const educationEntrySchema = z.object({
  id: z.string(),
  institution: z.string().min(1, { message: 'Institution is required' }),
  degree: z.string().min(1, { message: 'Degree is required' }),
  field: z.string().min(1, { message: 'Field of study is required' }),
  startDate: z.string().min(1, { message: 'Start date is required' }),
  endDate: z.string().optional(),
  current: z.boolean().default(false),
  location: z.string().min(1, { message: 'Location is required' }),
  description: z.string().optional(),
  certificateUrl: z.string().optional(),
  certificateOrder: z.number().optional(), // Added for certificate ordering
});

const educationArraySchema = z.array(educationEntrySchema);

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await Promise.resolve(params);

    // Check if the CV exists and belongs to the user
    const cv = await prisma.cV.findUnique({
      where: {
        id,
      },
    });

    if (!cv) {
      return NextResponse.json(
        { message: 'CV not found' },
        { status: 404 }
      );
    }

    if (cv.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const body = await req.json();

    // Support both formats: array of entries or { education: [...] }
    let educationEntries;
    if (Array.isArray(body)) {
      educationEntries = educationArraySchema.parse(body);
    } else if (body.education && Array.isArray(body.education)) {
      educationEntries = educationArraySchema.parse(body.education);
    } else {
      throw new Error('Invalid request format');
    }

    // Update the CV with the new education entries
    const updatedCV = await prisma.cV.update({
      where: {
        id,
      },
      data: {
        education: educationEntries,
      },
    });

    return NextResponse.json(updatedCV);
  } catch (error) {
    console.error('Education update error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid input data', errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
