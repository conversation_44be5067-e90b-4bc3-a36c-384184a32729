import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/db/prisma';
import { auth } from '@/lib/auth/auth';

const workExperienceSchema = z.object({
  id: z.string(),
  company: z.string().min(1, { message: 'Company is required' }),
  position: z.string().min(1, { message: 'Position is required' }),
  startDate: z.string().min(1, { message: 'Start date is required' }),
  endDate: z.string().optional(),
  current: z.boolean().default(false),
  location: z.string().min(1, { message: 'Location is required' }),
  description: z.string().optional(),
});

const workExperienceArraySchema = z.array(workExperienceSchema);

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await Promise.resolve(params);

    // Check if the CV exists and belongs to the user
    const cv = await prisma.cV.findUnique({
      where: {
        id,
      },
    });

    if (!cv) {
      return NextResponse.json(
        { message: 'CV not found' },
        { status: 404 }
      );
    }

    if (cv.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const body = await req.json();
    const workExperienceEntries = workExperienceArraySchema.parse(body);

    // Update the CV with the new work experience entries
    const updatedCV = await prisma.cV.update({
      where: {
        id,
      },
      data: {
        workExperience: workExperienceEntries,
      },
    });

    return NextResponse.json(updatedCV);
  } catch (error) {
    console.error('Work experience update error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid input data', errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
