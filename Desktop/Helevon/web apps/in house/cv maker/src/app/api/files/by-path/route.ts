import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db/prisma';
import { auth } from '@/lib/auth/auth';

/**
 * GET /api/files/by-path
 * Get a file by its original path
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get the path from the query string
    const url = new URL(request.url);
    const path = url.searchParams.get('path');

    if (!path) {
      return NextResponse.json(
        { message: 'Path parameter is required' },
        { status: 400 }
      );
    }

    console.log(`Looking for file with path: ${path}`);

    // Extract the file name from the path (last part of the path)
    const parts = path.split('/');
    const fileName = parts[parts.length - 1];
    const userId = parts[parts.length - 2];

    console.log(`Extracted file name: ${fileName}, user ID: ${userId}`);

    // Try to find the file by name pattern
    let file = await prisma.file.findFirst({
      where: {
        url: {
          contains: fileName
        }
      }
    });

    // If not found by name, try to find by URL pattern
    if (!file) {
      console.log(`File not found by name, trying URL pattern...`);
      file = await prisma.file.findFirst({
        where: {
          url: {
            contains: userId
          }
        }
      });
    }

    if (!file) {
      return NextResponse.json(
        { message: 'File not found' },
        { status: 404 }
      );
    }

    // Check if the file belongs to the user
    if (file.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Check if the file has data
    if (!file.fileData) {
      return NextResponse.json(
        { message: 'File data not found' },
        { status: 404 }
      );
    }

    // Return the file with appropriate headers
    return new NextResponse(Buffer.from(file.fileData, 'base64'), {
      headers: {
        'Content-Type': file.type,
        'Content-Disposition': `inline; filename="${file.name}"`,
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    });
  } catch (error) {
    console.error('File retrieval error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
