import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/auth';
import { CertificateService } from '@/lib/services/certificate-service';
import { validateFileUpload, sanitizeFilename } from '@/lib/utils/file-validator';

/**
 * POST /api/certificates
 * Upload a new certificate
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const educationEntryId = formData.get('educationEntryId') as string;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file
    const { valid, error } = validateFileUpload(file);
    if (!valid) {
      return NextResponse.json({ error }, { status: 400 });
    }

    // Sanitize filename
    const sanitizedFilename = sanitizeFilename(file.name);

    // Convert file to base64
    const buffer = await file.arrayBuffer();
    const base64 = Buffer.from(buffer).toString('base64');

    // Save certificate to database
    const certificate = await CertificateService.saveCertificate({
      userId: session.user.id,
      educationEntryId: educationEntryId ? parseInt(educationEntryId) : undefined,
      fileName: sanitizedFilename,
      fileType: file.type,
      fileSize: file.size,
      fileData: base64,
    });

    return NextResponse.json({
      id: certificate.id,
      fileName: certificate.fileName,
      fileType: certificate.fileType,
      fileSize: certificate.fileSize,
    });
  } catch (error) {
    console.error('Error uploading certificate:', error);
    return NextResponse.json({ error: 'Failed to upload certificate' }, { status: 500 });
  }
}

/**
 * GET /api/certificates
 * Get certificates for the current user
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const educationEntryId = url.searchParams.get('educationEntryId');

    if (!educationEntryId) {
      return NextResponse.json({ error: 'Education entry ID is required' }, { status: 400 });
    }

    const certificates = await CertificateService.getCertificatesByEducationEntryId(parseInt(educationEntryId));

    // Return certificates without the file data to reduce payload size
    return NextResponse.json(certificates.map(cert => ({
      id: cert.id,
      fileName: cert.fileName,
      fileType: cert.fileType,
      fileSize: cert.fileSize,
      createdAt: cert.createdAt,
    })));
  } catch (error) {
    console.error('Error getting certificates:', error);
    return NextResponse.json({ error: 'Failed to get certificates' }, { status: 500 });
  }
}
