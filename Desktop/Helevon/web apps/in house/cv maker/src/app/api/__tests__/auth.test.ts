// Import the prisma client
import { prisma } from '@/lib/db/prisma';

// Mock the NextRequest and NextResponse
const mockJson = jest.fn();
const mockNextResponse = {
  json: jest.fn().mockImplementation((data, options = {}) => ({
    status: options.status || 200,
    json: mockJson.mockResolvedValue(data),
  })),
};

// Mock the register handler
const mockRegisterHandler = jest.fn().mockImplementation(async (req) => {
  const body = await req.json();

  // Check if user exists
  const existingUser = await prisma.user.findUnique({
    where: { email: body.email },
  });

  if (existingUser) {
    return mockNextResponse.json(
      { message: 'User already exists' },
      { status: 400 }
    );
  }

  // Create new user
  const newUser = await prisma.user.create({
    data: {
      name: body.name,
      email: body.email,
      password: 'hashed_password', // Mock hashed password
    },
  });

  // Return user without password
  const { password, ...userWithoutPassword } = newUser;
  return mockNextResponse.json(userWithoutPassword, { status: 201 });
});

// Mock the modules
jest.mock('@/app/api/auth/register/route', () => ({
  POST: mockRegisterHandler,
}));

// Create a mock NextRequest
class MockNextRequest {
  constructor(url, options = {}) {
    this.url = url;
    this.method = options.method || 'GET';
    this._body = options.body || '';
  }

  async json() {
    return JSON.parse(this._body);
  }
}

// Mock Prisma
jest.mock('@/lib/db/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
  },
}));

// Mock bcrypt
jest.mock('bcrypt', () => ({
  hash: jest.fn(() => Promise.resolve('hashed_password')),
}));

describe('Auth API Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/register', () => {
    it('should register a new user successfully', async () => {
      // Mock Prisma findUnique to return null (user doesn't exist)
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      // Mock Prisma create to return a new user
      (prisma.user.create as jest.Mock).mockResolvedValue({
        id: 'test-id',
        name: 'Test User',
        email: '<EMAIL>',
        password: 'hashed_password',
      });

      // Create request with valid data
      const request = new MockNextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'Password123!',
        }),
      });

      // Call the handler
      const response = await mockRegisterHandler(request);

      // Check response
      expect(response.status).toBe(201);

      // Verify Prisma calls
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(prisma.user.create).toHaveBeenCalledWith({
        data: {
          name: 'Test User',
          email: '<EMAIL>',
          password: 'hashed_password',
        },
      });
    });

    it('should return 400 if user already exists', async () => {
      // Mock Prisma findUnique to return an existing user
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'existing-id',
        email: '<EMAIL>',
      });

      // Create request with existing email
      const request = new MockNextRequest('http://localhost:3000/api/auth/register', {
        method: 'POST',
        body: JSON.stringify({
          name: 'Test User',
          email: '<EMAIL>',
          password: 'Password123!',
        }),
      });

      // Call the handler
      const response = await mockRegisterHandler(request);

      // Check response
      expect(response.status).toBe(400);

      // Verify Prisma calls
      expect(prisma.user.findUnique).toHaveBeenCalledWith({
        where: { email: '<EMAIL>' },
      });
      expect(prisma.user.create).not.toHaveBeenCalled();
    });
  });
});
