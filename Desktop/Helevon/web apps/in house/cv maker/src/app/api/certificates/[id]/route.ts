import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/auth';
import { CertificateService } from '@/lib/services/certificate-service';

/**
 * GET /api/certificates/[id]
 * Get a specific certificate
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await Promise.resolve(params);
    const id = parseInt(resolvedParams.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid certificate ID' }, { status: 400 });
    }

    const certificate = await CertificateService.getCertificateById(id);

    if (!certificate) {
      return NextResponse.json({ error: 'Certificate not found' }, { status: 404 });
    }

    // Check if the certificate belongs to the current user
    if (certificate.userId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Return the file data as a response with the appropriate content type
    return new NextResponse(Buffer.from(certificate.fileData, 'base64'), {
      headers: {
        'Content-Type': certificate.fileType,
        'Content-Disposition': `inline; filename="${certificate.fileName}"`,
      },
    });
  } catch (error) {
    console.error('Error getting certificate:', error);
    return NextResponse.json({ error: 'Failed to get certificate' }, { status: 500 });
  }
}

/**
 * DELETE /api/certificates/[id]
 * Delete a certificate
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const resolvedParams = await Promise.resolve(params);
    const id = parseInt(resolvedParams.id);

    if (isNaN(id)) {
      return NextResponse.json({ error: 'Invalid certificate ID' }, { status: 400 });
    }

    // Soft delete the certificate
    const certificate = await CertificateService.deleteCertificate(id, session.user.id);

    if (!certificate) {
      return NextResponse.json({ error: 'Certificate not found or already deleted' }, { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting certificate:', error);
    return NextResponse.json({ error: 'Failed to delete certificate' }, { status: 500 });
  }
}
