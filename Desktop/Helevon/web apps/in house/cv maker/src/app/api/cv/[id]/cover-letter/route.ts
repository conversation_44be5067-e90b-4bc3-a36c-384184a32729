import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/db/prisma';
import { auth } from '@/lib/auth/auth';
import { CoverLetter } from '@/types/cv';

// Schema for structured cover letter
const coverLetterObjectSchema = z.object({
  // Recipient information
  recipientName: z.string().optional(),
  recipientCompany: z.string().optional(),
  recipientAddress: z.string().optional(),
  recipientCity: z.string().optional(),
  recipientPostalCode: z.string().optional(),
  recipientCountry: z.string().optional(),
  recipientEmail: z.string().optional(),
  recipientPhone: z.string().optional(),
  recipientOtherInfo: z.string().optional(),

  // Cover letter content
  subject: z.string().optional(),
  content: z.string().optional(),
  date: z.string().optional(),
  salutation: z.string().optional(),

  // Signature
  signatureType: z.enum(['text', 'image']).optional(),
  signatureImageUrl: z.string().optional(),
});

// Schema for the request body
const requestSchema = z.object({
  coverLetter: z.union([
    z.string(),
    coverLetterObjectSchema,
  ]),
});

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await Promise.resolve(params);

    // Check if the CV exists and belongs to the user
    const cv = await prisma.cV.findUnique({
      where: {
        id,
      },
    });

    if (!cv) {
      return NextResponse.json(
        { message: 'CV not found' },
        { status: 404 }
      );
    }

    if (cv.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const body = await req.json();
    const { coverLetter } = requestSchema.parse(body);

    // Update the CV with the new cover letter
    // Convert object to JSON string if it's an object
    const coverLetterValue = typeof coverLetter === 'object'
      ? JSON.stringify(coverLetter)
      : coverLetter;

    const updatedCV = await prisma.cV.update({
      where: {
        id,
      },
      data: {
        coverLetter: coverLetterValue,
      },
    });

    // If the cover letter has a signature image, update the file metadata
    if (typeof coverLetter === 'object' && coverLetter.signatureType === 'image' && coverLetter.signatureImageUrl) {
      // Find the file by URL
      const file = await prisma.file.findFirst({
        where: {
          url: coverLetter.signatureImageUrl,
          userId: session.user.id,
        },
      });

      if (file) {
        // Update the file to associate it with this CV and set category to signature
        await prisma.file.update({
          where: {
            id: file.id,
          },
          data: {
            cvId: id,
            category: 'signature',
          },
        });
      }
    }

    return NextResponse.json(updatedCV);
  } catch (error) {
    console.error('Cover letter update error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid input data', errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
