import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db/prisma';
import { auth } from '@/lib/auth/auth';

export async function GET(
  req: NextRequest,
  { params }: { params: { id: string; fileId: string } }
) {
  try {
    const resolvedParams = await Promise.resolve(params);
    const { id, fileId } = resolvedParams;

    console.log(`GET request for file: ${fileId} in CV: ${id}`);

    const session = await auth();

    if (!session?.user) {
      console.log('Unauthorized: No session');
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`User ID: ${session.user.id}`);

    // Find the file
    console.log(`Looking for file with ID: ${fileId}`);

    // Get all files for debugging
    const allFiles = await prisma.file.findMany({
      where: {
        cvId: id,
      },
    });

    console.log(`Found ${allFiles.length} files for CV ${id}:`);
    allFiles.forEach(f => {
      console.log(`- File ID: ${f.id}, Name: ${f.name}, Type: ${f.type}, Has Data: ${!!f.fileData}`);
    });

    // Try to find the file by ID first
    console.log(`Searching for file with ID: ${fileId}`);

    // Get all files in the database for debugging
    const allFilesInDb = await prisma.file.findMany({
      take: 20, // Limit to 20 files to avoid huge output
    });

    console.log(`All files in database (up to 20):`);
    allFilesInDb.forEach(f => {
      console.log(`- File ID: ${f.id}, Name: ${f.name}, URL: ${f.url}`);
    });

    let file = await prisma.file.findUnique({
      where: {
        id: fileId,
      },
    });

    // If not found by ID, try to find by URL
    if (!file) {
      console.log(`File not found by ID, trying URL path...`);
      const urlPath = `/api/cv/${id}/file/${fileId}`;
      console.log(`Looking for file with URL: ${urlPath}`);

      file = await prisma.file.findFirst({
        where: {
          url: urlPath,
        },
      });

      if (!file) {
        // Try a more flexible search
        console.log(`File not found by URL, trying partial match...`);
        file = await prisma.file.findFirst({
          where: {
            url: {
              contains: fileId
            }
          }
        });
      }
    }

    if (!file) {
      console.log(`File not found with ID: ${fileId}`);
      return NextResponse.json(
        { message: 'File not found' },
        { status: 404 }
      );
    }

    console.log(`Found file: ${file.name}, type: ${file.type}, has data: ${!!file.fileData}`);


    // Check if the file belongs to the user
    if (file.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Check if the file has data
    if (!file.fileData) {
      return NextResponse.json(
        { message: 'File data not found' },
        { status: 404 }
      );
    }

    // Convert base64 to buffer
    const buffer = Buffer.from(file.fileData, 'base64');

    // Return the file with appropriate headers
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': file.type,
        'Content-Disposition': `inline; filename="${file.name}"`,
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    });
  } catch (error) {
    console.error('File retrieval error:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string; fileId: string } }
) {
  try {
    const resolvedParams = await Promise.resolve(params);
    const { id, fileId } = resolvedParams;

    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Check if the CV exists and belongs to the user
    const cv = await prisma.cV.findUnique({
      where: {
        id,
      },
    });

    if (!cv) {
      return NextResponse.json(
        { message: 'CV not found' },
        { status: 404 }
      );
    }

    if (cv.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Find the file
    const file = await prisma.file.findFirst({
      where: {
        id: fileId,
      },
    });

    if (!file) {
      return NextResponse.json(
        { message: 'File not found' },
        { status: 404 }
      );
    }

    if (file.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Delete the file from the database (including the file data)
    await prisma.file.delete({
      where: {
        id: fileId,
      },
    });

    return NextResponse.json({ message: 'File deleted successfully' });
  } catch (error) {
    console.error('File deletion error:', error);

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
