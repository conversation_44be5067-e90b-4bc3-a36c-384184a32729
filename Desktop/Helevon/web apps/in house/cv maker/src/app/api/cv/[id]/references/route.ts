import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/db/prisma';
import { auth } from '@/lib/auth/auth';

const referenceSchema = z.object({
  id: z.string(),
  name: z.string().min(1, { message: 'Name is required' }),
  company: z.string().min(1, { message: 'Company is required' }),
  position: z.string().min(1, { message: 'Position is required' }),
  email: z.string().email({ message: 'Valid email is required' }).optional().nullable(),
  phone: z.string().optional().nullable(),
  relationship: z.string().min(1, { message: 'Relationship is required' }),
});

const referencesArraySchema = z.array(referenceSchema);

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await Promise.resolve(params);

    // Check if the CV exists and belongs to the user
    const cv = await prisma.cV.findUnique({
      where: {
        id,
      },
    });

    if (!cv) {
      return NextResponse.json(
        { message: 'CV not found' },
        { status: 404 }
      );
    }

    if (cv.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    const body = await req.json();
    const references = referencesArraySchema.parse(body);

    // Update the CV with the new references
    const updatedCV = await prisma.cV.update({
      where: {
        id,
      },
      data: {
        references: references,
      },
    });

    return NextResponse.json(updatedCV);
  } catch (error) {
    console.error('References update error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid input data', errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
