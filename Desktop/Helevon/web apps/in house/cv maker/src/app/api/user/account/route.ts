import { NextRequest, NextResponse } from 'next/server';
import { hash, compare } from 'bcrypt';
import { z } from 'zod';
import { prisma } from '@/lib/db/prisma';
import { auth } from '@/lib/auth/auth';

const accountSchema = z.object({
  name: z.string().min(1, { message: 'Name is required' }),
  email: z.string().email({ message: 'Valid email is required' }),
  language: z.enum(['en', 'de', 'ar']),
  currentPassword: z.string().optional(),
  newPassword: z.string().min(8, { message: 'Password must be at least 8 characters' }).optional(),
  confirmPassword: z.string().optional(),
});

export async function PUT(req: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { name, email, language, currentPassword, newPassword } = accountSchema.parse(body);

    // Check if email is already taken by another user
    const existingUser = await prisma.user.findUnique({
      where: {
        email,
      },
    });

    if (existingUser && existingUser.id !== session.user.id) {
      return NextResponse.json(
        { message: 'Email is already taken' },
        { status: 409 }
      );
    }

    // Get the current user
    const user = await prisma.user.findUnique({
      where: {
        id: session.user.id,
      },
    });

    if (!user) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      );
    }

    // Prepare update data
    const updateData: any = {
      name,
      email,
      language,
    };

    // If changing password, verify current password
    if (newPassword && currentPassword) {
      if (!user.password) {
        return NextResponse.json(
          { message: 'Cannot change password for accounts without a password' },
          { status: 400 }
        );
      }

      const isPasswordValid = await compare(currentPassword, user.password);

      if (!isPasswordValid) {
        return NextResponse.json(
          { message: 'Current password is incorrect' },
          { status: 400 }
        );
      }

      // Hash the new password
      updateData.password = await hash(newPassword, 10);
    }

    // Update the user
    const updatedUser = await prisma.user.update({
      where: {
        id: session.user.id,
      },
      data: updateData,
    });

    // Return the user without the password
    const { password: _, ...userWithoutPassword } = updatedUser;
    
    return NextResponse.json(userWithoutPassword);
  } catch (error) {
    console.error('Account update error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid input data', errors: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
