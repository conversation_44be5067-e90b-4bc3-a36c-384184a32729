import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { prisma } from '@/lib/db/prisma';
import { auth } from '@/lib/auth/auth';

const cvSchema = z.object({
  userId: z.string().uuid(),
  title: z.string().min(1, 'Title is required'),
  template: z.enum(['standard', 'modern', 'creative', 'german-ausbildung']),
  language: z.enum(['en', 'de']),
});

export async function POST(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { userId, title, template, language } = cvSchema.parse(body);

    // Verify that the user is creating a CV for themselves
    if (userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Create the CV with properly initialized JSON objects for structured data
    const cv = await prisma.cV.create({
      data: {
        userId,
        title,
        template,
        language,
        personalInfo: {
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          address: '',
          city: '',
          postalCode: '',
          country: '',
          dateOfBirth: '',
          nationality: '',
          profileSummary: '',
        },
        education: [],
        workExperience: [],
        skills: [],
        references: [],
        coverLetter: '',
      },
    });

    return NextResponse.json(cv, { status: 201 });
  } catch (error) {
    console.error('CV creation error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { message: 'Invalid input data', errors: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const cvs = await prisma.cV.findMany({
      where: {
        userId: session.user.id,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    return NextResponse.json(cvs);
  } catch (error) {
    console.error('CV fetch error:', error);

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
