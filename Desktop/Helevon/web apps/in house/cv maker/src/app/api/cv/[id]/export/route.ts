import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db/prisma';
import { auth } from '@/lib/auth/auth';
import { isPdfExportEnabled } from '@/lib/utils/pdf-export';
import { generatePdf } from '@/lib/services/pdf-service';
import { generateReactPdf } from '@/lib/services/react-pdf-service';

export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: cvId } = await Promise.resolve(params);

    // Check if the CV exists and belongs to the user
    const cv = await prisma.cV.findUnique({
      where: {
        id: cvId,
      },
      include: {
        files: true,
      },
    });

    if (!cv) {
      return NextResponse.json(
        { message: 'CV not found' },
        { status: 404 }
      );
    }

    if (cv.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Try to generate the PDF
    try {
      console.log('Starting PDF generation for CV:', cv.id);

      // Try to generate the PDF with React PDF first
      let pdfBuffer;
      try {
        console.log('Attempting to generate PDF with React PDF...');
        pdfBuffer = await generateReactPdf(cv, cv.files);
        console.log('Successfully generated PDF with React PDF');
      } catch (reactPdfError) {
        console.error('React PDF generation error details:', reactPdfError);
        console.log('React PDF generation failed, falling back to simple PDF');
        pdfBuffer = await generatePdf(cv, cv.files);
      }

      // If PDF generation is disabled, return a message
      if (!pdfBuffer) {
        console.log('PDF generation is disabled');
        return NextResponse.json({
          message: 'PDF export is currently disabled. Set CAN_EXPORT=true in your .env file to enable it.',
          cv: {
            id: cv.id,
            title: cv.title,
            template: cv.template,
          }
        });
      }

      console.log('PDF generated successfully, returning response');
      // Return the PDF as a response
      return new NextResponse(pdfBuffer, {
        headers: {
          'Content-Type': 'application/pdf',
          'Content-Disposition': `attachment; filename="${cv.title.replace(/\s+/g, '_')}.pdf"`,
        },
      });
    } catch (error) {
      console.error('PDF generation error:', error);
      // Return more detailed error information
      return NextResponse.json(
        {
          message: 'Failed to generate PDF',
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('PDF export error:', error);

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
