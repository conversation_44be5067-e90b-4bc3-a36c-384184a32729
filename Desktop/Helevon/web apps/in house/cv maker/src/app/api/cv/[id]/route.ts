import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth/auth';
import { prisma } from '@/lib/db/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await Promise.resolve(params);

    // Check if the CV exists and belongs to the user
    const cv = await prisma.cV.findUnique({
      where: {
        id,
      },
      include: {
        files: true,
      },
    });

    if (!cv) {
      return NextResponse.json({ message: 'CV not found' }, { status: 404 });
    }

    // Check if the CV belongs to the current user
    if (cv.userId !== session.user.id) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 403 });
    }

    return NextResponse.json(cv);
  } catch (error) {
    console.error('Error fetching CV:', error);
    return NextResponse.json(
      { message: 'Failed to fetch CV' },
      { status: 500 }
    );
  }
}
