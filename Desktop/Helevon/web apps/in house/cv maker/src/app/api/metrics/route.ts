import { NextRequest, NextResponse } from 'next/server';

// Simple placeholder metrics API for Netlify deployment
export async function GET(request: NextRequest) {
  return new NextResponse(JSON.stringify({
    status: 'ok',
    message: 'Application is running',
    timestamp: new Date().toISOString()
  }), {
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// Empty metrics object to avoid breaking imports
export const metrics = {
  requestCount: 0,
  errorCount: 0,
  responseTimeTotal: 0,
  responseTimeCount: 0
};
