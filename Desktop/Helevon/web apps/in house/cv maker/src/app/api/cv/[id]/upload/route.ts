import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { prisma } from '@/lib/db/prisma';
import { auth } from '@/lib/auth/auth';

export async function POST(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();

    if (!session?.user) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = await Promise.resolve(params);

    // Check if the CV exists and belongs to the user
    const cv = await prisma.cV.findUnique({
      where: {
        id,
      },
    });

    if (!cv) {
      return NextResponse.json(
        { message: 'CV not found' },
        { status: 404 }
      );
    }

    if (cv.userId !== session.user.id) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 403 }
      );
    }

    // Parse the form data
    const formData = await req.formData();
    const file = formData.get('file') as File;
    const category = formData.get('category') as string;

    if (!file) {
      return NextResponse.json(
        { message: 'No file provided' },
        { status: 400 }
      );
    }

    if (!category) {
      return NextResponse.json(
        { message: 'No category provided' },
        { status: 400 }
      );
    }

    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { message: 'File is too large. Maximum size is 5MB.' },
        { status: 400 }
      );
    }

    // Validate file type based on category
    const allowedTypes: Record<string, string[]> = {
      photo: ['image/jpeg', 'image/png', 'image/jpg'],
      certificate: ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'],
      cover_letter: ['application/pdf'],
      signature: ['image/jpeg', 'image/png', 'image/jpg'],
      other: ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'],
    };

    if (!allowedTypes[category]?.includes(file.type)) {
      return NextResponse.json(
        { message: 'Invalid file type for the selected category' },
        { status: 400 }
      );
    }

    // Create a unique filename for reference
    const fileId = uuidv4();
    const fileExtension = file.name.split('.').pop();
    const fileName = `${fileId}.${fileExtension}`;

    // Create a virtual URL for the file (used for client-side references)
    // Use the file ID as the identifier for retrieval
    const fileUrl = `/api/cv/${id}/file/${fileId}`;

    console.log(`Creating file with ID: ${fileId} and URL: ${fileUrl}`);

    // Convert file to base64 for database storage
    const fileBuffer = Buffer.from(await file.arrayBuffer());
    const fileData = fileBuffer.toString('base64');

    // Save the file data and metadata to the database
    const fileRecord = await prisma.file.create({
      data: {
        id: fileId, // Use the same ID that we generated for the URL
        userId: session.user.id,
        cvId: id,
        name: file.name,
        type: file.type,
        size: file.size,
        url: fileUrl,
        fileData: fileData,
        category: category,
      },
    });

    console.log(`Created file record with ID: ${fileRecord.id}, URL: ${fileRecord.url}`);

    // If this is a photo upload and there's an existing photo, delete it
    if (category === 'photo') {
      const existingPhotos = await prisma.file.findMany({
        where: {
          userId: session.user.id,
          cvId: id,
          category: 'photo',
          id: {
            not: fileRecord.id,
          },
        },
      });

      if (existingPhotos.length > 0) {
        await prisma.file.deleteMany({
          where: {
            id: {
              in: existingPhotos.map(photo => photo.id),
            },
          },
        });
      }
    }

    return NextResponse.json(fileRecord);
  } catch (error) {
    console.error('File upload error:', error);

    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}
