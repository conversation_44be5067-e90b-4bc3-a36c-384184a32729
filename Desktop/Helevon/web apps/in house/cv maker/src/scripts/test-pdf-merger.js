/**
 * Test script for PDF merger functionality
 * 
 * This script tests the PDF merger utility by creating a simple PDF and merging it with a certificate.
 * 
 * Usage:
 * node src/scripts/test-pdf-merger.js
 */

const { PDFDocument } = require('pdf-lib');
const fs = require('fs').promises;
const path = require('path');

async function createTestPdf() {
  // Create a new PDF document
  const pdfDoc = await PDFDocument.create();
  
  // Add a page
  const page = pdfDoc.addPage([595.28, 841.89]); // A4 size
  
  // Add some text
  page.drawText('Test PDF Document', {
    x: 50,
    y: 800,
    size: 30,
  });
  
  page.drawText('This is a test PDF document to test the PDF merger functionality.', {
    x: 50,
    y: 750,
    size: 12,
  });
  
  // Save the PDF
  const pdfBytes = await pdfDoc.save();
  
  // Write to file
  const outputPath = path.resolve(process.cwd(), 'public', 'test-main.pdf');
  await fs.writeFile(outputPath, pdfBytes);
  
  console.log(`Test PDF created at: ${outputPath}`);
  
  return pdfBytes;
}

async function mergePdfs(mainPdfBytes, certificatePath) {
  try {
    // Load the main PDF document
    const mainPdfDoc = await PDFDocument.load(mainPdfBytes);
    
    // Read the certificate PDF
    const certificateBytes = await fs.readFile(certificatePath);
    
    // Load the certificate PDF
    const certificatePdf = await PDFDocument.load(certificateBytes);
    
    // Get all pages from the certificate PDF
    const certificatePages = await mainPdfDoc.copyPages(
      certificatePdf, 
      certificatePdf.getPageIndices()
    );
    
    // Add all pages from the certificate to the main PDF
    for (const page of certificatePages) {
      mainPdfDoc.addPage(page);
    }
    
    console.log('Successfully merged certificate');
    
    // Save the merged PDF
    const mergedPdfBytes = await mainPdfDoc.save();
    
    // Write to file
    const outputPath = path.resolve(process.cwd(), 'public', 'test-merged.pdf');
    await fs.writeFile(outputPath, mergedPdfBytes);
    
    console.log(`Merged PDF created at: ${outputPath}`);
    
    return mergedPdfBytes;
  } catch (error) {
    console.error('Error merging PDFs:', error);
    throw error;
  }
}

async function main() {
  try {
    // Create a test PDF
    const mainPdfBytes = await createTestPdf();
    
    // Check if a certificate path was provided
    const certificatePath = process.argv[2] || path.resolve(process.cwd(), 'public', 'sample-certificate.pdf');
    
    // Check if the certificate exists
    try {
      await fs.access(certificatePath);
    } catch (error) {
      console.error(`Certificate not found at: ${certificatePath}`);
      console.log('Please provide a valid certificate path as an argument or create a sample-certificate.pdf in the public directory.');
      return;
    }
    
    // Merge the PDFs
    await mergePdfs(mainPdfBytes, certificatePath);
    
    console.log('Test completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
main();
