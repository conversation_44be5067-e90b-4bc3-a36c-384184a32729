{"extends": "next/core-web-vitals", "rules": {"@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-explicit-any": "off", "react/no-unescaped-entities": "off", "prefer-const": "off", "@typescript-eslint/no-empty-object-type": "off", "@typescript-eslint/no-unused-expressions": "off", "jsx-a11y/alt-text": "off", "@next/next/no-img-element": "off", "@typescript-eslint/no-require-imports": "off", "react-hooks/exhaustive-deps": "off", "import/no-anonymous-default-export": "off"}, "ignorePatterns": ["node_modules/", ".next/", "out/", "public/", "**/*.d.ts"]}