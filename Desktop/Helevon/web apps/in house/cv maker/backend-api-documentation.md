# Backend API Documentation

## Overview

This document provides comprehensive documentation for the CV Maker application's backend API. The application is built with Next.js API routes, Prisma ORM, NextAuth.js for authentication, and PostgreSQL database.

## Architecture

- **Framework**: Next.js 15.3.1 with App Router
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js with JWT strategy
- **File Storage**: Base64 encoded files stored in database
- **PDF Generation**: React PDF and pdf-lib libraries
- **Validation**: Zod schemas for request/response validation

## Authentication

### Authentication System
- **Provider**: NextAuth.js with Credentials provider
- **Strategy**: JWT-based sessions
- **Password Hashing**: bcrypt
- **Session Management**: Server-side session validation

### Authentication Endpoints

#### POST `/api/auth/[...nextauth]`
NextAuth.js dynamic route handling all authentication flows.

**Supported Operations:**
- `POST /api/auth/signin` - User login
- `POST /api/auth/signout` - User logout
- `GET /api/auth/session` - Get current session
- `GET /api/auth/csrf` - Get CSRF token

#### POST `/api/auth/verify-password`
Verifies password against hashed password.

**Request Body:**
```json
{
  "password": "string",
  "hashedPassword": "string"
}
```

**Response:**
```json
{
  "isValid": "boolean"
}
```

## User Management

### User Account Management

#### GET `/api/user/account`
Get current user account information.

**Authentication**: Required
**Response:**
```json
{
  "id": "string",
  "name": "string",
  "email": "string",
  "language": "en|de|ar",
  "createdAt": "string",
  "updatedAt": "string"
}
```

#### PUT `/api/user/account`
Update user account information.

**Authentication**: Required
**Request Body:**
```json
{
  "name": "string",
  "email": "string",
  "language": "en|de|ar",
  "currentPassword": "string?",
  "newPassword": "string?",
  "confirmPassword": "string?"
}
```

**Validation Rules:**
- Name: Required, minimum 1 character
- Email: Valid email format required
- Language: Must be one of 'en', 'de', 'ar'
- New password: Minimum 8 characters if provided
- Current password: Required if changing password

## CV Management

### CV CRUD Operations

#### POST `/api/cv`
Create a new CV.

**Authentication**: Required
**Request Body:**
```json
{
  "userId": "string (UUID)",
  "title": "string",
  "template": "standard|modern|creative|german-ausbildung",
  "language": "en|de"
}
```

**Response:**
```json
{
  "id": "string",
  "userId": "string",
  "title": "string",
  "template": "string",
  "language": "string",
  "personalInfo": {},
  "education": [],
  "workExperience": [],
  "skills": [],
  "references": [],
  "coverLetter": "",
  "createdAt": "string",
  "updatedAt": "string"
}
```

#### GET `/api/cv`
Get all CVs for the authenticated user.

**Authentication**: Required
**Response:**
```json
[
  {
    "id": "string",
    "userId": "string",
    "title": "string",
    "template": "string",
    "language": "string",
    "personalInfo": "object",
    "education": "array",
    "workExperience": "array",
    "skills": "array",
    "references": "array",
    "coverLetter": "string",
    "createdAt": "string",
    "updatedAt": "string"
  }
]
```

#### GET `/api/cv/[id]`
Get a specific CV by ID.

**Authentication**: Required
**Path Parameters:**
- `id`: CV UUID

**Response:**
```json
{
  "id": "string",
  "userId": "string",
  "title": "string",
  "template": "string",
  "language": "string",
  "personalInfo": "object",
  "education": "array",
  "workExperience": "array",
  "skills": "array",
  "references": "array",
  "coverLetter": "string",
  "files": [
    {
      "id": "string",
      "name": "string",
      "url": "string",
      "category": "string"
    }
  ],
  "createdAt": "string",
  "updatedAt": "string"
}
```

#### PUT `/api/cv/[id]`
Update a CV.

**Authentication**: Required
**Path Parameters:**
- `id`: CV UUID

**Request Body:** Same as POST `/api/cv` but all fields optional

#### DELETE `/api/cv/[id]`
Delete a CV.

**Authentication**: Required
**Path Parameters:**
- `id`: CV UUID

**Response:**
```json
{
  "message": "CV deleted successfully"
}
```

### CV Section Updates

#### PUT `/api/cv/[id]/personal-info`
Update personal information section.

**Authentication**: Required
**Request Body:**
```json
{
  "firstName": "string",
  "lastName": "string",
  "email": "string",
  "phone": "string",
  "address": "string",
  "city": "string",
  "postalCode": "string",
  "country": "string",
  "dateOfBirth": "string?",
  "placeOfBirth": "string?",
  "nationality": "string?",
  "maritalStatus": "string?",
  "photoUrl": "string?"
}
```

#### PUT `/api/cv/[id]/education`
Update education section.

**Authentication**: Required
**Request Body:**
```json
{
  "education": [
    {
      "id": "string",
      "institution": "string",
      "degree": "string",
      "fieldOfStudy": "string",
      "startDate": "string",
      "endDate": "string?",
      "isCurrentlyStudying": "boolean",
      "grade": "string?",
      "description": "string?",
      "certificates": ["string"]
    }
  ]
}
```

#### PUT `/api/cv/[id]/work-experience`
Update work experience section.

**Authentication**: Required
**Request Body:**
```json
{
  "workExperience": [
    {
      "id": "string",
      "company": "string",
      "position": "string",
      "startDate": "string",
      "endDate": "string?",
      "isCurrentlyWorking": "boolean",
      "description": "string?",
      "location": "string?"
    }
  ]
}
```

#### PUT `/api/cv/[id]/skills`
Update skills section.

**Authentication**: Required
**Request Body:**
```json
{
  "skills": [
    {
      "id": "string",
      "name": "string",
      "category": "technical|language|soft",
      "level": "beginner|intermediate|advanced|expert|native"
    }
  ]
}
```

#### PUT `/api/cv/[id]/references`
Update references section.

**Authentication**: Required
**Request Body:**
```json
{
  "references": [
    {
      "id": "string",
      "name": "string",
      "position": "string",
      "company": "string",
      "email": "string",
      "phone": "string"
    }
  ]
}
```

#### PUT `/api/cv/[id]/cover-letter`
Update cover letter.

**Authentication**: Required
**Request Body:**
```json
{
  "coverLetter": "string | object"
}
```

## File Management

### File Upload

#### POST `/api/cv/[id]/upload`
Upload a file associated with a CV.

**Authentication**: Required
**Content-Type**: `multipart/form-data`
**Form Data:**
- `file`: File to upload
- `category`: `photo|certificate|cover_letter|other`

**File Constraints:**
- Maximum size: 5MB (configurable via MAX_UPLOAD_SIZE_MB)
- Supported types: PDF, JPEG, PNG, JPG
- Storage: Base64 encoded in database

**Response:**
```json
{
  "id": "string",
  "userId": "string",
  "cvId": "string",
  "name": "string",
  "type": "string",
  "size": "number",
  "url": "string",
  "category": "string",
  "createdAt": "string",
  "updatedAt": "string"
}
```

### File Retrieval

#### GET `/api/cv/[id]/file/[fileId]`
Retrieve a file by ID.

**Authentication**: Required
**Path Parameters:**
- `id`: CV UUID
- `fileId`: File UUID

**Response**: Binary file data with appropriate Content-Type header

## PDF Export

### PDF Generation

#### GET `/api/cv/[id]/export`
Generate and download CV as PDF.

**Authentication**: Required
**Path Parameters:**
- `id`: CV UUID

**Query Parameters:**
- `format`: `pdf` (default)

**Response**: PDF file with Content-Type: `application/pdf`

**Features:**
- Multiple template support
- Certificate merging
- Multilingual support (German/English)
- Fallback PDF generation if React PDF fails

**Environment Variables:**
- `CAN_EXPORT`: Enable/disable PDF export functionality

## Data Models

### User Model
```typescript
interface User {
  id: string;
  name?: string;
  email?: string;
  emailVerified?: Date;
  password?: string;
  image?: string;
  language: string; // Default: "en"
  createdAt: Date;
  updatedAt: Date;
  lastLoginAt?: Date;
  loginAttempts: number; // Default: 0
  lockedUntil?: Date;
}
```

### CV Model
```typescript
interface CV {
  id: string;
  userId: string;
  title: string;
  template: string; // Default: "standard"
  language: string; // Default: "de"
  personalInfo: Json;
  education: Json;
  workExperience: Json;
  skills: Json;
  references: Json;
  coverLetter?: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### File Model
```typescript
interface File {
  id: string;
  userId: string;
  cvId?: string;
  name: string;
  type: string; // MIME type
  size: number;
  url: string;
  fileData?: string; // Base64 encoded
  category: string;
  createdAt: Date;
  updatedAt: Date;
}
```

### Certificate Model
```typescript
interface Certificate {
  id: number;
  userId: string;
  educationEntryId?: number;
  fileName: string;
  fileType: string;
  fileSize: number;
  fileData: string; // Base64 encoded
  createdAt: Date;
  updatedAt: Date;
  isDeleted: boolean; // Default: false
}
```

## Error Handling

### Standard Error Responses

#### 400 Bad Request
```json
{
  "message": "Invalid input data",
  "errors": [
    {
      "field": "string",
      "message": "string"
    }
  ]
}
```

#### 401 Unauthorized
```json
{
  "message": "Unauthorized"
}
```

#### 403 Forbidden
```json
{
  "message": "Unauthorized"
}
```

#### 404 Not Found
```json
{
  "message": "CV not found"
}
```

#### 500 Internal Server Error
```json
{
  "message": "Internal server error"
}
```

## Security Features

### Authentication & Authorization
- JWT-based session management
- Password hashing with bcrypt
- User ownership validation for all CV operations
- Rate limiting (configurable)
- Account lockout after failed attempts

### Data Validation
- Zod schema validation for all inputs
- File type and size validation
- SQL injection prevention via Prisma ORM
- XSS protection through input sanitization

### Security Headers
- X-Frame-Options: DENY
- X-XSS-Protection: 1; mode=block
- X-Content-Type-Options: nosniff
- Referrer-Policy: strict-origin-when-cross-origin
- Content-Security-Policy with strict rules

## Environment Variables

### Required Variables
```env
DATABASE_URL=postgresql://user:password@host:port/database
NEXTAUTH_SECRET=your-secret-key
NEXTAUTH_URL=http://localhost:3000
```

### Optional Variables
```env
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000
MAX_UPLOAD_SIZE_MB=5
CAN_EXPORT=true
NEXT_PUBLIC_BASE_URL=http://localhost:3000
```

## Database Schema

The application uses PostgreSQL with Prisma ORM. Key tables:
- `User` - User accounts and authentication
- `Account` - NextAuth.js account linking
- `Session` - NextAuth.js sessions
- `VerificationToken` - NextAuth.js verification tokens
- `CV` - CV documents with JSON fields for structured data
- `File` - File uploads with base64 storage
- `Certificate` - Certificate files linked to education entries

## Business Logic

### CV Creation Flow
1. User authentication validation
2. Input validation with Zod schemas
3. CV creation with initialized empty sections
4. Database transaction for consistency

### File Upload Flow
1. Authentication and ownership validation
2. File type and size validation
3. Base64 encoding and database storage
4. URL generation for file access
5. Duplicate handling (e.g., single photo per CV)

### PDF Generation Flow
1. CV data retrieval and validation
2. Template selection and data processing
3. React PDF rendering with fallback
4. Certificate merging if applicable
5. Binary response with appropriate headers

### Internationalization
- Multi-language support (English, German, Arabic)
- Template-specific language handling
- Localized PDF generation
- User language preferences

This API provides a complete backend service for CV creation, management, and export functionality with robust security, validation, and error handling.
