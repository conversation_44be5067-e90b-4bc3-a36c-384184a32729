import { test, expect } from '@playwright/test';

test.describe('Home Page', () => {
  test('should display the home page correctly', async ({ page }) => {
    await page.goto('/');

    // Check page title
    await expect(page).toHaveTitle(/CV Maker/);

    // Check header
    const header = page.locator('header');
    await expect(header).toBeVisible();
    await expect(page.getByText(/CV Maker by/i)).toBeVisible();

    // Check navigation links
    await expect(page.getByRole('link', { name: /home/<USER>
    await expect(page.getByRole('link', { name: /login/i })).toBeVisible();
    await expect(page.getByRole('link', { name: /register/i })).toBeVisible();

    // Check footer
    const footer = page.locator('footer');
    await expect(footer).toBeVisible();
  });

  test('should navigate to login page', async ({ page }) => {
    await page.goto('/');

    // Check if cookie consent dialog is present and accept it
    const cookieConsentDialog = page.locator('div[role="dialog"]').filter({ hasText: /cookie/i });
    if (await cookieConsentDialog.isVisible()) {
      await page.getByRole('button', { name: /accept/i }).click();
    }

    // Click login link
    await page.getByRole('link', { name: /login/i }).click();

    // Check URL
    await expect(page).toHaveURL(/.*login/);

    // Check login form
    await expect(page.getByRole('heading', { name: /login/i })).toBeVisible();
    await expect(page.getByLabel(/email/i)).toBeVisible();
    await expect(page.getByLabel(/password/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();
  });

  test('should navigate to register page', async ({ page }) => {
    await page.goto('/');

    // Check if cookie consent dialog is present and accept it
    const cookieConsentDialog = page.locator('div[role="dialog"]').filter({ hasText: /cookie/i });
    if (await cookieConsentDialog.isVisible()) {
      await page.getByRole('button', { name: /accept/i }).click();
    }

    // Click register link
    await page.getByRole('link', { name: /register/i }).click();

    // Check URL
    await expect(page).toHaveURL(/.*register/);

    // Check registration form
    await expect(page.getByRole('heading', { name: /register/i })).toBeVisible();
    await expect(page.getByLabel(/name/i)).toBeVisible();
    await expect(page.getByLabel(/email/i)).toBeVisible();
    await expect(page.getByLabel(/password/i)).toBeVisible();
    await expect(page.getByRole('button', { name: /sign up/i })).toBeVisible();
  });
});
