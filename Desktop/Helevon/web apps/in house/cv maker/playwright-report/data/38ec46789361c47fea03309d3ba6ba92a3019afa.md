# Test info

- Name: Home Page >> should display the home page correctly
- Location: /home/<USER>/Desktop/Helevon/cv maker/cv-application-maker/tests/e2e/home.spec.ts:4:7

# Error details

```
Error: browserType.launch: 
╔══════════════════════════════════════════════════════╗
║ Host system is missing dependencies to run browsers. ║
║ Missing libraries:                                   ║
║     libevent-2.1.so.7                                ║
║     libflite.so.1                                    ║
║     libflite_usenglish.so.1                          ║
║     libflite_cmu_grapheme_lang.so.1                  ║
║     libflite_cmu_grapheme_lex.so.1                   ║
║     libflite_cmu_indic_lang.so.1                     ║
║     libflite_cmu_indic_lex.so.1                      ║
║     libflite_cmulex.so.1                             ║
║     libflite_cmu_time_awb.so.1                       ║
║     libflite_cmu_us_awb.so.1                         ║
║     libflite_cmu_us_kal16.so.1                       ║
║     libflite_cmu_us_kal.so.1                         ║
║     libflite_cmu_us_rms.so.1                         ║
║     libflite_cmu_us_slt.so.1                         ║
║     libavif.so.13                                    ║
╚══════════════════════════════════════════════════════╝
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 |
   3 | test.describe('Home Page', () => {
>  4 |   test('should display the home page correctly', async ({ page }) => {
     |       ^ Error: browserType.launch: 
   5 |     await page.goto('/');
   6 |     
   7 |     // Check page title
   8 |     await expect(page).toHaveTitle(/CV Maker/);
   9 |     
  10 |     // Check header
  11 |     const header = page.locator('header');
  12 |     await expect(header).toBeVisible();
  13 |     await expect(header.getByText('CV Maker by Helevon Technology')).toBeVisible();
  14 |     
  15 |     // Check navigation links
  16 |     await expect(page.getByRole('link', { name: /home/<USER>
  17 |     await expect(page.getByRole('link', { name: /login/i })).toBeVisible();
  18 |     await expect(page.getByRole('link', { name: /register/i })).toBeVisible();
  19 |     
  20 |     // Check footer
  21 |     const footer = page.locator('footer');
  22 |     await expect(footer).toBeVisible();
  23 |   });
  24 |   
  25 |   test('should navigate to login page', async ({ page }) => {
  26 |     await page.goto('/');
  27 |     
  28 |     // Click login link
  29 |     await page.getByRole('link', { name: /login/i }).click();
  30 |     
  31 |     // Check URL
  32 |     await expect(page).toHaveURL(/.*login/);
  33 |     
  34 |     // Check login form
  35 |     await expect(page.getByRole('heading', { name: /login/i })).toBeVisible();
  36 |     await expect(page.getByLabel(/email/i)).toBeVisible();
  37 |     await expect(page.getByLabel(/password/i)).toBeVisible();
  38 |     await expect(page.getByRole('button', { name: /sign in/i })).toBeVisible();
  39 |   });
  40 |   
  41 |   test('should navigate to register page', async ({ page }) => {
  42 |     await page.goto('/');
  43 |     
  44 |     // Click register link
  45 |     await page.getByRole('link', { name: /register/i }).click();
  46 |     
  47 |     // Check URL
  48 |     await expect(page).toHaveURL(/.*register/);
  49 |     
  50 |     // Check registration form
  51 |     await expect(page.getByRole('heading', { name: /register/i })).toBeVisible();
  52 |     await expect(page.getByLabel(/name/i)).toBeVisible();
  53 |     await expect(page.getByLabel(/email/i)).toBeVisible();
  54 |     await expect(page.getByLabel(/password/i)).toBeVisible();
  55 |     await expect(page.getByRole('button', { name: /sign up/i })).toBeVisible();
  56 |   });
  57 | });
  58 |
```