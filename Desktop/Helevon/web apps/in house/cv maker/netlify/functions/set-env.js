// netlify/functions/set-env.js
// This function runs before the build to ensure the DATABASE_URL is set correctly

exports.handler = async function(event, context) {
  // Set the DATABASE_URL environment variable
  process.env.DATABASE_URL = "postgresql://helevon-cv-generator-lucifer-as-admin:tuV1%3D%5D%7DQJw%3Ed%2AQ%2Bz@93.127.213.33:5438/helevon-cv-generator-db";
  
  console.log('DATABASE_URL set for Netlify functions');
  
  return {
    statusCode: 200,
    body: JSON.stringify({ message: "Environment variables set" })
  };
};
