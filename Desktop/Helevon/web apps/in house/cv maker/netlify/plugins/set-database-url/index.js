// netlify/plugins/set-database-url/index.js
// This plugin ensures the DATABASE_URL is set correctly for Netlify

module.exports = {
  onPreBuild: ({ utils }) => {
    // Set the DATABASE_URL environment variable
    process.env.DATABASE_URL = "postgresql://helevon-cv-generator-lucifer-as-admin:tuV1%3D%5D%7DQJw%3Ed%2AQ%2Bz@93.127.213.33:5438/helevon-cv-generator-db";
    
    console.log('DATABASE_URL set by custom plugin');
    
    // Write to .env file as well
    const fs = require('fs');
    fs.writeFileSync('.env', `DATABASE_URL="postgresql://helevon-cv-generator-lucifer-as-admin:tuV1%3D%5D%7DQJw%3Ed%2AQ%2Bz@93.127.213.33:5438/helevon-cv-generator-db"\n`);
    
    console.log('DATABASE_URL written to .env file');
  }
};
