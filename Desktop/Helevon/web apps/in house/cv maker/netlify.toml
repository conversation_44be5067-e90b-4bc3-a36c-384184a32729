[build]
  command = "npm run build"
  publish = ".next"
  ignore = "git diff --quiet $CACHED_COMMIT_REF $COMMIT_REF ./src/ ./prisma/ ./public/"

[build.environment]
  NEXT_TELEMETRY_DISABLED = "1"
  NODE_VERSION = "20"
  NPM_FLAGS = "--no-audit --production=false"
  # Direct database URL with properly escaped special characters and protocol prefix
  DATABASE_URL = "postgresql://helevon-cv-generator-lucifer-as-admin:tuV1%3D%5D%7DQJw%3Ed%2AQ%2Bz@93.127.213.33:5438/helevon-cv-generator-db"

[[plugins]]
  package = "@netlify/plugin-nextjs"

[[plugins]]
  package = "./netlify/plugins/set-database-url"

# Properly handle Next.js static assets
[[headers]]
  for = "/_next/static/*"
  [headers.values]
    Cache-Control = "public, max-age=********, immutable"

# Handle Next.js routing
[[redirects]]
  from = "/_next/*"
  to = "/_next/:splat"
  status = 200
  force = false

# Handle API routes
[[redirects]]
  from = "/api/*"
  to = "/api/:splat"
  status = 200
  force = false

# Force HTTPS
[[redirects]]
  from = "http://*"
  to = "https://:splat"
  status = 301
  force = true

# Security headers
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(), interest-cohort=()"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; font-src 'self' data:; connect-src 'self'; frame-src 'self'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; object-src 'none'; upgrade-insecure-requests;"
